#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超精确删除方案 - 只删除明确的页眉页脚，绝不误删正文
"""

import fitz  # PyMuPDF
import os
import re

def ultra_precise_analysis(pdf_path):
    """
    超精确分析，只识别明确的页眉页脚
    """
    
    print(f"🔍 超精确分析PDF文件: {pdf_path}")
    print("策略: 只识别明确无误的页眉页脚，绝不误删正文")
    print("="*80)
    
    doc = fitz.open(pdf_path)
    total_pages = len(doc)
    
    # 分析更多页面以获得准确模式
    sample_pages = list(range(0, min(50, total_pages), 2))  # 每隔一页分析
    
    print(f"📄 总页数: {total_pages}")
    print(f"🔍 分析页面: {len(sample_pages)} 页")
    
    # 收集明确的页眉页脚
    confirmed_headers = set()
    confirmed_footers = set()
    header_positions = []
    footer_positions = []
    
    for page_num in sample_pages:
        page = doc[page_num]
        rect = page.rect
        
        text_dict = page.get_text("dict")
        
        # 分析每个文本元素
        for block in text_dict["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]
                            y_pos = bbox[1]
                            
                            # 超严格的页眉检测
                            if y_pos < 80:  # 只检查最顶部
                                if is_definite_header(text):
                                    confirmed_headers.add(text)
                                    header_positions.append(y_pos)
                                    print(f"   ✓ 确认页眉: 第{page_num+1}页 - '{text}' (Y:{y_pos:.1f})")
                            
                            # 超严格的页脚检测
                            elif y_pos > 680:  # 只检查最底部
                                if is_definite_footer(text, page_num):
                                    confirmed_footers.add(text)
                                    footer_positions.append(y_pos)
                                    print(f"   ✓ 确认页脚: 第{page_num+1}页 - '{text}' (Y:{y_pos:.1f})")
    
    doc.close()
    
    result = {
        "confirmed_headers": confirmed_headers,
        "confirmed_footers": confirmed_footers,
        "header_y_max": max(header_positions) if header_positions else 0,
        "footer_y_min": min(footer_positions) if footer_positions else 754
    }
    
    print(f"\n📊 确认的页眉页脚:")
    print(f"   确认页眉: {len(confirmed_headers)} 种")
    if confirmed_headers:
        for header in list(confirmed_headers)[:5]:
            print(f"     - '{header}'")
    
    print(f"   确认页脚: {len(confirmed_footers)} 种")
    if confirmed_footers:
        for footer in list(confirmed_footers)[:10]:
            print(f"     - '{footer}'")
    
    print(f"   页眉最大Y位置: {result['header_y_max']:.1f}")
    print(f"   页脚最小Y位置: {result['footer_y_min']:.1f}")
    
    return result

def is_definite_header(text):
    """
    超严格判断是否为页眉
    只识别明确无误的页眉模式
    """
    
    text_clean = text.strip()
    
    # 明确的项目标题模式
    if re.match(r'^项目[一二三四五六七八九十\d]+$', text_clean):
        return True
    
    # 明确的页眉标题（完全匹配）
    definite_headers = {
        '动力电池的发展历程',
        '动力电池安全与防护措施',
        '动力电池基本知识',
        '动力电池管理系统检修',
        '动力电池维护与检查',
        '动力电池故障诊断',
        '动力电池充电系统检修',
        '新能源汽车动力电池构造与检修'  # 添加这个页眉
    }
    
    if text_clean in definite_headers:
        return True
    
    return False

def is_definite_footer(text, page_num):
    """
    超严格判断是否为页脚
    只识别明确的页码
    """
    
    text_clean = text.strip()
    
    # 只识别纯数字页码
    if text_clean.isdigit() and 1 <= len(text_clean) <= 3:
        try:
            page_number = int(text_clean)
            # 页码应该在合理范围内
            if 1 <= page_number <= 200:
                return True
        except:
            pass
    
    return False

def ultra_precise_removal(input_pdf, output_pdf, analysis_result):
    """
    超精确删除，只删除确认的页眉页脚
    """
    
    print(f"\n🎯 开始超精确删除")
    print("策略: 只删除100%确认的页眉页脚文本")
    print("="*60)
    
    try:
        doc = fitz.open(input_pdf)
        total_pages = len(doc)
        removed_count = 0
        
        for page_num in range(total_pages):
            if (page_num + 1) % 30 == 0 or page_num == 0:
                print(f"📈 处理进度: {page_num + 1}/{total_pages}")
            
            page = doc[page_num]
            text_dict = page.get_text("dict")
            areas_to_remove = []
            
            for block in text_dict["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"].strip()
                            if text:
                                bbox = span["bbox"]
                                y_pos = bbox[1]
                                
                                should_remove = False
                                
                                # 只删除确认的页眉
                                if y_pos <= analysis_result["header_y_max"] + 5:
                                    if text in analysis_result["confirmed_headers"] or is_definite_header(text):
                                        should_remove = True
                                
                                # 只删除确认的页脚
                                elif y_pos >= analysis_result["footer_y_min"] - 5:
                                    if text in analysis_result["confirmed_footers"] or is_definite_footer(text, page_num):
                                        should_remove = True
                                
                                if should_remove:
                                    areas_to_remove.append({
                                        "text": text,
                                        "bbox": bbox
                                    })
                                    removed_count += 1
            
            # 精确删除
            for area in areas_to_remove:
                bbox = area["bbox"]
                # 使用精确的边界
                rect_to_remove = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
                page.add_redact_annot(rect_to_remove, fill=(1, 1, 1))
            
            if areas_to_remove:
                page.apply_redactions()
        
        # 保存时使用最佳压缩
        doc.save(output_pdf, garbage=4, deflate=True, clean=True)
        doc.close()
        
        print(f"\n✅ 超精确删除完成！")
        print(f"📊 删除统计: {removed_count} 个确认的页眉页脚元素")
        
        # 文件大小对比
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        size_change = ((new_size - original_size) / original_size) * 100
        
        print(f"📈 文件大小对比:")
        print(f"   原文件: {original_size:.2f} MB")
        print(f"   新文件: {new_size:.2f} MB")
        print(f"   大小变化: {size_change:+.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 删除失败: {e}")
        return False

def verify_no_content_loss(input_pdf, output_pdf):
    """
    验证没有误删正文内容
    """
    
    print(f"\n🔍 验证内容完整性")
    
    try:
        # 打开两个文件进行对比
        original_doc = fitz.open(input_pdf)
        processed_doc = fitz.open(output_pdf)
        
        # 检查几个页面的正文内容
        sample_pages = [10, 30, 50, 80, 100]
        sample_pages = [p for p in sample_pages if p < len(original_doc)]
        
        for page_num in sample_pages:
            original_page = original_doc[page_num]
            processed_page = processed_doc[page_num]
            
            # 获取中间区域的文本（正文区域）
            rect = original_page.rect
            content_area = fitz.Rect(
                rect.x0,
                rect.y0 + 120,  # 跳过页眉区域
                rect.x1,
                rect.y1 - 80    # 跳过页脚区域
            )
            
            original_text = original_page.get_text(clip=content_area)
            processed_text = processed_page.get_text(clip=content_area)
            
            # 比较正文内容长度
            original_length = len(original_text.strip())
            processed_length = len(processed_text.strip())
            
            if processed_length < original_length * 0.95:  # 如果正文减少超过5%
                print(f"   ⚠️  第{page_num+1}页可能有正文丢失")
                print(f"      原始正文长度: {original_length}")
                print(f"      处理后长度: {processed_length}")
            else:
                print(f"   ✅ 第{page_num+1}页正文完整")
        
        original_doc.close()
        processed_doc.close()
        
        print(f"✅ 内容完整性验证完成")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

def main():
    """主函数"""
    input_file = "新能源汽车动力电池构造与检修（第二次正文）_未命名.pdf"
    output_file = "新能源汽车动力电池构造与检修（第二次正文）_超精确删除.pdf"
    
    if not os.path.exists(input_file):
        print(f"❌ 找不到文件: {input_file}")
        return
    
    print("🎯 超精确页眉页脚删除工具")
    print("="*60)
    print("特点:")
    print("✓ 超严格检测，只删除100%确认的页眉页脚")
    print("✓ 绝不误删正文内容")
    print("✓ 保持页面完整")
    print("✓ 内容完整性验证")
    print("="*60)
    
    # 步骤1: 超精确分析
    analysis_result = ultra_precise_analysis(input_file)
    
    # 步骤2: 超精确删除
    success = ultra_precise_removal(input_file, output_file, analysis_result)
    
    if success:
        # 步骤3: 验证内容完整性
        verify_no_content_loss(input_file, output_file)
        
        print(f"\n🎉 超精确处理完成！")
        print(f"📁 输出文件: {output_file}")
        print(f"💡 已验证正文内容完整性")
    else:
        print(f"\n❌ 处理失败")

if __name__ == "__main__":
    main()
