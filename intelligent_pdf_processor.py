#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能PDF页眉页脚删除器
采用"分析→理解→确定→删除"的四阶段处理流程
对每一页进行精确的结构分析和内容理解
"""

import fitz  # PyMuPDF
import os
import re
from collections import defaultdict, Counter
from dataclasses import dataclass
from typing import List, Dict, Tuple, Set
import statistics

@dataclass
class TextElement:
    """文本元素数据结构"""
    text: str
    bbox: Tuple[float, float, float, float]  # (x0, y0, x1, y1)
    font_size: float
    font_name: str
    page_num: int
    
    @property
    def y_position(self) -> float:
        return self.bbox[1]
    
    @property
    def x_position(self) -> float:
        return self.bbox[0]
    
    @property
    def width(self) -> float:
        return self.bbox[2] - self.bbox[0]
    
    @property
    def height(self) -> float:
        return self.bbox[3] - self.bbox[1]

@dataclass
class PageStructure:
    """页面结构分析结果"""
    page_num: int
    page_height: float
    page_width: float
    top_elements: List[TextElement]      # 顶部区域元素
    middle_elements: List[TextElement]   # 中部区域元素
    bottom_elements: List[TextElement]   # 底部区域元素
    all_elements: List[TextElement]      # 所有元素

class PageAnalyzer:
    """页面分析器 - 负责单页结构分析"""
    
    def __init__(self):
        self.top_ratio = 0.15      # 顶部区域占比
        self.bottom_ratio = 0.15   # 底部区域占比
    
    def analyze_page(self, page: fitz.Page, page_num: int) -> PageStructure:
        """分析单个页面的结构"""
        
        rect = page.rect
        page_height = rect.height
        page_width = rect.width
        
        # 定义区域边界
        top_boundary = page_height * self.top_ratio
        bottom_boundary = page_height * (1 - self.bottom_ratio)
        
        # 提取所有文本元素
        text_dict = page.get_text("dict")
        all_elements = []
        
        for block in text_dict["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            element = TextElement(
                                text=text,
                                bbox=tuple(span["bbox"]),
                                font_size=span["size"],
                                font_name=span["font"],
                                page_num=page_num
                            )
                            all_elements.append(element)
        
        # 按区域分类
        top_elements = [e for e in all_elements if e.y_position < top_boundary]
        bottom_elements = [e for e in all_elements if e.y_position > bottom_boundary]
        middle_elements = [e for e in all_elements if top_boundary <= e.y_position <= bottom_boundary]
        
        return PageStructure(
            page_num=page_num,
            page_height=page_height,
            page_width=page_width,
            top_elements=top_elements,
            middle_elements=middle_elements,
            bottom_elements=bottom_elements,
            all_elements=all_elements
        )

class ContentUnderstanding:
    """内容理解器 - 负责理解页面内容的语义和结构"""
    
    def __init__(self):
        self.header_keywords = {
            '项目', '动力电池', '新能源汽车', '构造', '检修', '管理系统',
            '安全', '防护', '维护', '检查', '故障', '诊断', '充电系统'
        }
        
        self.content_indicators = {
            '电池', '充电', '系统', '技术', '应用', '发展', '性能', '容量',
            '电压', '电流', '温度', '安全', '保护', '监控', '控制'
        }
    
    def analyze_text_characteristics(self, elements: List[TextElement]) -> Dict:
        """分析文本特征"""
        
        if not elements:
            return {}
        
        # 统计字体大小
        font_sizes = [e.font_size for e in elements]
        
        # 统计Y位置
        y_positions = [e.y_position for e in elements]
        
        # 统计文本长度
        text_lengths = [len(e.text) for e in elements]
        
        return {
            'font_size_avg': statistics.mean(font_sizes),
            'font_size_std': statistics.stdev(font_sizes) if len(font_sizes) > 1 else 0,
            'y_position_avg': statistics.mean(y_positions),
            'y_position_std': statistics.stdev(y_positions) if len(y_positions) > 1 else 0,
            'text_length_avg': statistics.mean(text_lengths),
            'element_count': len(elements)
        }
    
    def is_likely_header_content(self, text: str) -> bool:
        """判断是否可能是页眉内容"""
        
        text_lower = text.lower()
        
        # 项目标题模式
        if re.match(r'^项目[一二三四五六七八九十\d]+', text):
            return True
        
        # 包含页眉关键词
        header_score = sum(1 for keyword in self.header_keywords if keyword in text)
        if header_score >= 2:
            return True
        
        # 短文本且包含关键词
        if len(text) < 30 and header_score >= 1:
            return True
        
        return False
    
    def is_likely_footer_content(self, text: str) -> bool:
        """判断是否可能是页脚内容"""
        
        text_clean = text.strip()
        
        # 纯数字页码
        if text_clean.isdigit() and 1 <= len(text_clean) <= 4:
            try:
                page_num = int(text_clean)
                return 1 <= page_num <= 500
            except:
                pass
        
        # 页码格式
        page_patterns = [
            r'^\d+$',                    # 纯数字
            r'^第\s*\d+\s*页$',          # "第X页"
            r'^\d+\s*/\s*\d+$',          # "X/Y"
            r'^-\s*\d+\s*-$',            # "-X-"
        ]
        
        for pattern in page_patterns:
            if re.match(pattern, text_clean):
                return True
        
        return False
    
    def is_likely_content(self, text: str) -> bool:
        """判断是否可能是正文内容"""
        
        # 长文本通常是正文
        if len(text) > 100:
            return True
        
        # 包含正文指示词
        content_score = sum(1 for indicator in self.content_indicators if indicator in text)
        if content_score >= 2 and len(text) > 30:
            return True
        
        # 包含句子结构
        if any(punct in text for punct in ['。', '，', '；', '：', '！', '？']):
            return True
        
        return False

class HeaderFooterDetector:
    """页眉页脚检测器 - 负责准确确定页眉页脚内容"""
    
    def __init__(self):
        self.content_understanding = ContentUnderstanding()
        self.min_occurrence_threshold = 3  # 最小出现次数阈值
    
    def detect_headers_footers(self, page_structures: List[PageStructure]) -> Tuple[Set[str], Set[str]]:
        """检测页眉页脚内容"""
        
        print("🔍 开始智能检测页眉页脚...")
        
        # 收集候选页眉页脚
        header_candidates = defaultdict(list)  # text -> [TextElement, ...]
        footer_candidates = defaultdict(list)
        
        for structure in page_structures:
            # 分析顶部元素（页眉候选）
            for element in structure.top_elements:
                if self.content_understanding.is_likely_header_content(element.text):
                    header_candidates[element.text].append(element)
            
            # 分析底部元素（页脚候选）
            for element in structure.bottom_elements:
                if self.content_understanding.is_likely_footer_content(element.text):
                    footer_candidates[element.text].append(element)
        
        # 基于出现频率和位置一致性确定页眉页脚
        confirmed_headers = self._confirm_headers(header_candidates)
        confirmed_footers = self._confirm_footers(footer_candidates)
        
        print(f"✅ 检测完成:")
        print(f"   确认页眉: {len(confirmed_headers)} 种")
        print(f"   确认页脚: {len(confirmed_footers)} 种")
        
        return confirmed_headers, confirmed_footers
    
    def _confirm_headers(self, candidates: Dict[str, List[TextElement]]) -> Set[str]:
        """确认页眉"""
        
        confirmed = set()
        
        for text, elements in candidates.items():
            if len(elements) >= self.min_occurrence_threshold:
                # 检查位置一致性
                y_positions = [e.y_position for e in elements]
                y_std = statistics.stdev(y_positions) if len(y_positions) > 1 else 0
                
                # 位置相对稳定的才确认为页眉
                if y_std < 20:  # Y位置标准差小于20pt
                    confirmed.add(text)
                    print(f"   ✓ 页眉: '{text}' (出现{len(elements)}次, Y标准差:{y_std:.1f})")
        
        return confirmed
    
    def _confirm_footers(self, candidates: Dict[str, List[TextElement]]) -> Set[str]:
        """确认页脚"""
        
        confirmed = set()
        
        for text, elements in candidates.items():
            if len(elements) >= self.min_occurrence_threshold:
                # 对于页码，允许更灵活的判断
                if text.isdigit():
                    confirmed.add(text)
                    print(f"   ✓ 页脚: '{text}' (页码)")
                else:
                    # 其他页脚内容需要位置一致性
                    y_positions = [e.y_position for e in elements]
                    y_std = statistics.stdev(y_positions) if len(y_positions) > 1 else 0
                    
                    if y_std < 20:
                        confirmed.add(text)
                        print(f"   ✓ 页脚: '{text}' (出现{len(elements)}次)")
        
        return confirmed

class PreciseRemover:
    """精确删除器 - 负责精确删除页眉页脚，保护正文"""

    def __init__(self):
        self.content_understanding = ContentUnderstanding()

    def remove_headers_footers(self, input_pdf: str, output_pdf: str,
                             confirmed_headers: Set[str], confirmed_footers: Set[str]) -> bool:
        """精确删除页眉页脚"""

        print(f"\n🎯 开始精确删除: {input_pdf}")
        print("策略: 只删除确认的页眉页脚，绝对保护正文内容")
        print("="*80)

        try:
            doc = fitz.open(input_pdf)
            total_pages = len(doc)
            removed_count = 0
            protected_count = 0

            for page_num in range(total_pages):
                if (page_num + 1) % 20 == 0 or page_num == 0:
                    print(f"📈 处理进度: {page_num + 1}/{total_pages}")

                page = doc[page_num]
                rect = page.rect

                # 定义安全区域边界
                safe_top = rect.height * 0.2      # 顶部20%为页眉区域
                safe_bottom = rect.height * 0.8   # 底部20%为页脚区域

                text_dict = page.get_text("dict")
                areas_to_remove = []

                for block in text_dict["blocks"]:
                    if "lines" in block:
                        for line in block["lines"]:
                            for span in line["spans"]:
                                text = span["text"].strip()
                                if text:
                                    bbox = span["bbox"]
                                    y_pos = bbox[1]

                                    should_remove = False
                                    removal_reason = ""

                                    # 页眉区域检查
                                    if y_pos < safe_top:
                                        if text in confirmed_headers:
                                            should_remove = True
                                            removal_reason = "确认页眉"
                                        elif self._is_additional_header_pattern(text):
                                            should_remove = True
                                            removal_reason = "页眉模式"
                                        else:
                                            # 保护可能的正文内容
                                            if self.content_understanding.is_likely_content(text):
                                                protected_count += 1
                                                if protected_count <= 5:  # 只显示前5个保护的内容
                                                    print(f"   🛡️  保护顶部内容: '{text[:30]}...'")

                                    # 页脚区域检查
                                    elif y_pos > safe_bottom:
                                        if text in confirmed_footers:
                                            should_remove = True
                                            removal_reason = "确认页脚"
                                        elif self._is_additional_footer_pattern(text):
                                            should_remove = True
                                            removal_reason = "页脚模式"
                                        else:
                                            # 保护可能的正文内容
                                            if self.content_understanding.is_likely_content(text):
                                                protected_count += 1
                                                if protected_count <= 5:  # 只显示前5个保护的内容
                                                    print(f"   🛡️  保护底部内容: '{text[:30]}...'")

                                    if should_remove:
                                        areas_to_remove.append({
                                            "text": text,
                                            "bbox": bbox,
                                            "reason": removal_reason
                                        })
                                        removed_count += 1

                # 执行删除
                for area in areas_to_remove:
                    bbox = area["bbox"]
                    rect_to_remove = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
                    page.add_redact_annot(rect_to_remove, fill=(1, 1, 1))

                if areas_to_remove:
                    page.apply_redactions()

            # 保存文件
            doc.save(output_pdf, garbage=4, deflate=True, clean=True)
            doc.close()

            print(f"\n✅ 精确删除完成！")
            print(f"📊 处理统计:")
            print(f"   删除元素: {removed_count} 个")
            print(f"   保护元素: {protected_count} 个")

            # 文件大小对比
            original_size = os.path.getsize(input_pdf) / (1024 * 1024)
            new_size = os.path.getsize(output_pdf) / (1024 * 1024)
            size_change = ((new_size - original_size) / original_size) * 100

            print(f"📈 文件大小对比:")
            print(f"   原文件: {original_size:.2f} MB")
            print(f"   新文件: {new_size:.2f} MB")
            print(f"   大小变化: {size_change:+.1f}%")

            return True

        except Exception as e:
            print(f"❌ 删除失败: {e}")
            return False

    def _is_additional_header_pattern(self, text: str) -> bool:
        """检查额外的页眉模式"""

        # 项目标题变体
        if re.match(r'^项目[一二三四五六七八九十\d]+', text):
            return True

        # 常见页眉模式
        header_patterns = [
            r'^第[一二三四五六七八九十\d]+章',
            r'^第[一二三四五六七八九十\d]+节',
            r'^任务[一二三四五六七八九十\d]+',
        ]

        for pattern in header_patterns:
            if re.match(pattern, text):
                return True

        return False

    def _is_additional_footer_pattern(self, text: str) -> bool:
        """检查额外的页脚模式"""

        # 页码的各种格式
        footer_patterns = [
            r'^\d+$',                    # 纯数字
            r'^第\s*\d+\s*页$',          # "第X页"
            r'^\d+\s*/\s*\d+$',          # "X/Y"
            r'^-\s*\d+\s*-$',            # "-X-"
            r'^\(\s*\d+\s*\)$',          # "(X)"
        ]

        for pattern in footer_patterns:
            if re.match(pattern, text.strip()):
                return True

        return False

class IntelligentPDFProcessor:
    """智能PDF处理器 - 主控制器"""

    def __init__(self):
        self.page_analyzer = PageAnalyzer()
        self.content_understanding = ContentUnderstanding()
        self.header_footer_detector = HeaderFooterDetector()
        self.precise_remover = PreciseRemover()

    def process_pdf(self, input_pdf: str, output_pdf: str) -> bool:
        """
        智能处理PDF文件
        采用四阶段处理流程：分析→理解→确定→删除
        """

        print("🎯 智能PDF页眉页脚删除器")
        print("="*80)
        print("处理流程: 分析 → 理解 → 确定 → 删除")
        print("特点: 智能识别，精确删除，保护正文")
        print("="*80)

        if not os.path.exists(input_pdf):
            print(f"❌ 找不到文件: {input_pdf}")
            return False

        try:
            # 阶段1: 分析 - 对每一页进行结构分析
            print("\n📊 阶段1: 页面结构分析")
            page_structures = self._analyze_all_pages(input_pdf)

            # 阶段2: 理解 - 理解页面内容的语义和结构
            print("\n🧠 阶段2: 内容理解分析")
            self._understand_content_structure(page_structures)

            # 阶段3: 确定 - 准确确定页眉页脚内容
            print("\n🎯 阶段3: 页眉页脚确定")
            confirmed_headers, confirmed_footers = self.header_footer_detector.detect_headers_footers(page_structures)

            # 阶段4: 删除 - 精确删除页眉页脚
            print("\n🗑️  阶段4: 精确删除")
            success = self.precise_remover.remove_headers_footers(
                input_pdf, output_pdf, confirmed_headers, confirmed_footers
            )

            if success:
                # 验证处理结果
                print("\n🔍 阶段5: 结果验证")
                self._verify_processing_result(input_pdf, output_pdf)

                print(f"\n🎉 智能处理完成！")
                print(f"📁 输出文件: {output_pdf}")
                print(f"💡 已确保正文内容完整性")
                return True
            else:
                return False

        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return False

    def _analyze_all_pages(self, pdf_path: str) -> List[PageStructure]:
        """分析所有页面的结构"""

        doc = fitz.open(pdf_path)
        total_pages = len(doc)
        page_structures = []

        print(f"📄 总页数: {total_pages}")
        print("开始逐页分析...")

        for page_num in range(total_pages):
            if (page_num + 1) % 30 == 0 or page_num == 0:
                print(f"   分析进度: {page_num + 1}/{total_pages}")

            page = doc[page_num]
            structure = self.page_analyzer.analyze_page(page, page_num)
            page_structures.append(structure)

        doc.close()

        print(f"✅ 页面分析完成")
        print(f"   平均每页顶部元素: {sum(len(s.top_elements) for s in page_structures) / len(page_structures):.1f}")
        print(f"   平均每页底部元素: {sum(len(s.bottom_elements) for s in page_structures) / len(page_structures):.1f}")
        print(f"   平均每页中部元素: {sum(len(s.middle_elements) for s in page_structures) / len(page_structures):.1f}")

        return page_structures

    def _understand_content_structure(self, page_structures: List[PageStructure]):
        """理解内容结构"""

        print("分析内容特征...")

        # 统计顶部区域特征
        all_top_elements = []
        all_bottom_elements = []
        all_middle_elements = []

        for structure in page_structures:
            all_top_elements.extend(structure.top_elements)
            all_bottom_elements.extend(structure.bottom_elements)
            all_middle_elements.extend(structure.middle_elements)

        top_chars = self.content_understanding.analyze_text_characteristics(all_top_elements)
        bottom_chars = self.content_understanding.analyze_text_characteristics(all_bottom_elements)
        middle_chars = self.content_understanding.analyze_text_characteristics(all_middle_elements)

        print(f"✅ 内容特征分析完成")
        if top_chars:
            print(f"   顶部区域: 平均字体{top_chars['font_size_avg']:.1f}pt, 平均长度{top_chars['text_length_avg']:.1f}字符")
        if bottom_chars:
            print(f"   底部区域: 平均字体{bottom_chars['font_size_avg']:.1f}pt, 平均长度{bottom_chars['text_length_avg']:.1f}字符")
        if middle_chars:
            print(f"   中部区域: 平均字体{middle_chars['font_size_avg']:.1f}pt, 平均长度{middle_chars['text_length_avg']:.1f}字符")

    def _verify_processing_result(self, input_pdf: str, output_pdf: str):
        """验证处理结果"""

        try:
            # 检查文件是否存在
            if not os.path.exists(output_pdf):
                print("❌ 输出文件不存在")
                return

            # 简单的页数检查
            original_doc = fitz.open(input_pdf)
            processed_doc = fitz.open(output_pdf)

            original_pages = len(original_doc)
            processed_pages = len(processed_doc)

            if original_pages == processed_pages:
                print(f"✅ 页数验证通过: {processed_pages} 页")
            else:
                print(f"⚠️  页数不匹配: 原始{original_pages}页 vs 处理后{processed_pages}页")

            # 检查几个样本页面的内容完整性
            sample_pages = [10, 30, 50, 80, 100]
            sample_pages = [p for p in sample_pages if p < processed_pages]

            content_preserved = 0
            for page_num in sample_pages:
                original_page = original_doc[page_num]
                processed_page = processed_doc[page_num]

                # 获取中间区域的文本（正文区域）
                rect = original_page.rect
                content_area = fitz.Rect(
                    rect.x0,
                    rect.y0 + rect.height * 0.25,  # 跳过页眉区域
                    rect.x1,
                    rect.y1 - rect.height * 0.25   # 跳过页脚区域
                )

                original_text = original_page.get_text(clip=content_area)
                processed_text = processed_page.get_text(clip=content_area)

                # 比较正文内容长度
                if len(processed_text.strip()) >= len(original_text.strip()) * 0.95:
                    content_preserved += 1

            if content_preserved == len(sample_pages):
                print(f"✅ 内容完整性验证通过: {len(sample_pages)}个样本页面正文完整")
            else:
                print(f"⚠️  内容完整性警告: {content_preserved}/{len(sample_pages)}个样本页面通过验证")

            original_doc.close()
            processed_doc.close()

        except Exception as e:
            print(f"❌ 验证失败: {e}")

def main():
    """主函数"""

    input_file = "新能源汽车动力电池构造与检修（第二次正文）_未命名.pdf"
    output_file = "新能源汽车动力电池构造与检修（第二次正文）_智能删除版.pdf"

    # 创建智能处理器
    processor = IntelligentPDFProcessor()

    # 执行智能处理
    success = processor.process_pdf(input_file, output_file)

    if success:
        print(f"\n🎊 处理成功完成！")
        print(f"📁 输出文件: {output_file}")
        print(f"🔧 处理方式: 智能分析 + 精确删除")
        print(f"🛡️  正文保护: 已启用")
    else:
        print(f"\n❌ 处理失败")

if __name__ == "__main__":
    main()
