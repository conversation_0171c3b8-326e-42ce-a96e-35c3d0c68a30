#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确文本删除方案 - 只删除页眉页脚文本，不裁切页面
增加分析页面数，精确定位并删除页眉页脚内容
"""

import fitz  # PyMuPDF
import os
import re

def comprehensive_analysis(pdf_path, analysis_pages=20):
    """
    全面分析PDF文件，增加分析页面数
    
    Args:
        pdf_path (str): PDF文件路径
        analysis_pages (int): 分析页面数量
    
    Returns:
        dict: 分析结果
    """
    
    print(f"🔍 全面分析PDF文件: {pdf_path}")
    print(f"📊 分析页面数量: {analysis_pages}")
    print("="*80)
    
    doc = fitz.open(pdf_path)
    total_pages = len(doc)
    
    # 选择要分析的页面（均匀分布）
    if analysis_pages >= total_pages:
        sample_pages = list(range(total_pages))
    else:
        step = total_pages // analysis_pages
        sample_pages = [i * step for i in range(analysis_pages)]
        if sample_pages[-1] != total_pages - 1:
            sample_pages.append(total_pages - 1)
    
    print(f"📄 总页数: {total_pages}")
    print(f"🔍 分析页面: {sample_pages[:10]}{'...' if len(sample_pages) > 10 else ''}")
    print()
    
    # 收集页眉页脚模式
    header_patterns = []
    footer_patterns = []
    header_positions = []
    footer_positions = []
    
    for page_num in sample_pages:
        page = doc[page_num]
        rect = page.rect
        
        text_dict = page.get_text("dict")
        
        for block in text_dict["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]
                            y_pos = bbox[1]
                            
                            # 页眉检测（顶部15%）
                            if y_pos < rect.height * 0.15:
                                if is_header_content(text, page_num):
                                    header_patterns.append({
                                        "text": text,
                                        "bbox": bbox,
                                        "page": page_num + 1,
                                        "y_pos": y_pos
                                    })
                                    header_positions.append(y_pos)
                            
                            # 页脚检测（底部15%）
                            elif y_pos > rect.height * 0.85:
                                if is_footer_content(text, page_num):
                                    footer_patterns.append({
                                        "text": text,
                                        "bbox": bbox,
                                        "page": page_num + 1,
                                        "y_pos": y_pos
                                    })
                                    footer_positions.append(y_pos)
    
    doc.close()
    
    # 分析结果
    analysis_result = {
        "total_pages": total_pages,
        "analyzed_pages": len(sample_pages),
        "header_patterns": header_patterns,
        "footer_patterns": footer_patterns,
        "header_y_range": (min(header_positions), max(header_positions)) if header_positions else None,
        "footer_y_range": (min(footer_positions), max(footer_positions)) if footer_positions else None
    }
    
    # 显示分析结果
    print("📊 页眉分析结果:")
    if header_patterns:
        print(f"   检测到 {len(header_patterns)} 个页眉元素")
        print(f"   Y位置范围: {analysis_result['header_y_range'][0]:.1f} - {analysis_result['header_y_range'][1]:.1f}")
        
        # 显示前5个页眉示例
        for i, pattern in enumerate(header_patterns[:5]):
            print(f"   示例{i+1}: 第{pattern['page']}页 - '{pattern['text']}' (Y:{pattern['y_pos']:.1f})")
        if len(header_patterns) > 5:
            print(f"   ... 还有 {len(header_patterns) - 5} 个页眉")
    else:
        print("   未检测到页眉")
    
    print("\n📊 页脚分析结果:")
    if footer_patterns:
        print(f"   检测到 {len(footer_patterns)} 个页脚元素")
        print(f"   Y位置范围: {analysis_result['footer_y_range'][0]:.1f} - {analysis_result['footer_y_range'][1]:.1f}")
        
        # 显示前5个页脚示例
        for i, pattern in enumerate(footer_patterns[:5]):
            print(f"   示例{i+1}: 第{pattern['page']}页 - '{pattern['text']}' (Y:{pattern['y_pos']:.1f})")
        if len(footer_patterns) > 5:
            print(f"   ... 还有 {len(footer_patterns) - 5} 个页脚")
    else:
        print("   未检测到页脚")
    
    return analysis_result

def is_header_content(text, page_num):
    """判断是否为页眉内容"""
    
    text_clean = text.strip().lower()
    
    # 项目标题模式
    if re.match(r'^项目[一二三四五六七八九十\d]+', text):
        return True
    
    # 常见页眉关键词
    header_keywords = [
        '动力电池', '新能源汽车', '电池管理', '充电系统', 
        '安全防护', '维护检查', '故障诊断', '构造检修'
    ]
    
    for keyword in header_keywords:
        if keyword in text:
            return True
    
    # 短文本且在页面顶部
    if len(text_clean) < 30 and any(char in text for char in ['项目', '电池', '汽车']):
        return True
    
    return False

def is_footer_content(text, page_num):
    """判断是否为页脚内容"""
    
    text_clean = text.strip()
    
    # 纯数字页码
    if text_clean.isdigit() and 1 <= len(text_clean) <= 4:
        try:
            page_number = int(text_clean)
            if 1 <= page_number <= 1000:  # 合理的页码范围
                return True
        except:
            pass
    
    # 其他页脚模式
    footer_patterns = [
        r'^\d+$',                    # 纯数字
        r'^第\s*\d+\s*页$',          # "第X页"
        r'^\d+\s*/\s*\d+$',          # "X/Y"
        r'^-\s*\d+\s*-$',            # "-X-"
    ]
    
    for pattern in footer_patterns:
        if re.match(pattern, text_clean):
            return True
    
    return False

def precise_text_removal(input_pdf, output_pdf, analysis_result):
    """
    精确删除页眉页脚文本，不裁切页面
    """
    
    print(f"\n🎯 开始精确文本删除: {input_pdf}")
    print("删除策略: 只删除页眉页脚文本，保持页面完整")
    print("="*60)
    
    try:
        doc = fitz.open(input_pdf)
        total_pages = len(doc)
        removed_count = 0
        
        for page_num in range(total_pages):
            if (page_num + 1) % 20 == 0 or page_num == 0:
                print(f"📈 处理进度: {page_num + 1}/{total_pages}")
            
            page = doc[page_num]
            rect = page.rect
            
            text_dict = page.get_text("dict")
            areas_to_remove = []
            
            for block in text_dict["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"].strip()
                            if text:
                                bbox = span["bbox"]
                                y_pos = bbox[1]
                                
                                should_remove = False
                                removal_reason = ""
                                
                                # 基于分析结果的精确判断
                                if analysis_result["header_y_range"] and \
                                   analysis_result["header_y_range"][0] <= y_pos <= analysis_result["header_y_range"][1]:
                                    if is_header_content(text, page_num):
                                        should_remove = True
                                        removal_reason = "页眉"
                                
                                elif analysis_result["footer_y_range"] and \
                                     analysis_result["footer_y_range"][0] <= y_pos <= analysis_result["footer_y_range"][1]:
                                    if is_footer_content(text, page_num):
                                        should_remove = True
                                        removal_reason = "页脚"
                                
                                if should_remove:
                                    areas_to_remove.append({
                                        "text": text,
                                        "bbox": bbox,
                                        "reason": removal_reason
                                    })
                                    removed_count += 1
            
            # 删除识别出的页眉页脚
            for area in areas_to_remove:
                bbox = area["bbox"]
                # 创建精确的删除区域
                rect_to_remove = fitz.Rect(
                    bbox[0] - 1,  # 左边界稍微扩大
                    bbox[1] - 1,  # 上边界稍微扩大
                    bbox[2] + 1,  # 右边界稍微扩大
                    bbox[3] + 1   # 下边界稍微扩大
                )
                
                # 使用白色填充删除文本
                page.add_redact_annot(rect_to_remove, fill=(1, 1, 1))
            
            # 应用删除操作
            if areas_to_remove:
                page.apply_redactions()
        
        # 保存修改后的PDF
        doc.save(output_pdf)
        doc.close()
        
        print(f"\n✅ 精确文本删除完成！")
        print(f"📊 删除统计:")
        print(f"   删除文本元素: {removed_count} 个")
        print(f"   输出文件: {output_pdf}")
        
        # 显示文件大小对比
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        size_change = ((new_size - original_size) / original_size) * 100
        
        print(f"📈 文件大小对比:")
        print(f"   原文件: {original_size:.2f} MB")
        print(f"   新文件: {new_size:.2f} MB")
        print(f"   大小变化: {size_change:+.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 精确删除失败: {e}")
        return False

def main():
    """主函数"""
    input_file = "新能源汽车动力电池构造与检修（第二次正文）_未命名.pdf"
    output_file = "新能源汽车动力电池构造与检修（第二次正文）_精确删除版.pdf"
    
    if not os.path.exists(input_file):
        print(f"❌ 找不到文件: {input_file}")
        return
    
    print("🎯 精确页眉页脚文本删除工具")
    print("="*60)
    print("特点:")
    print("✓ 增加分析页面数量，提高准确性")
    print("✓ 不使用页面裁切，保持完整页面")
    print("✓ 只删除页眉页脚文本内容")
    print("✓ 保留所有正文和图片")
    print("="*60)
    
    # 步骤1: 全面分析
    analysis_result = comprehensive_analysis(input_file, analysis_pages=30)
    
    # 步骤2: 精确删除
    success = precise_text_removal(input_file, output_file, analysis_result)
    
    if success:
        print(f"\n🎉 处理完成！")
        print(f"📁 输出文件: {output_file}")
        print(f"💡 页面尺寸保持不变，只删除了页眉页脚文本")
    else:
        print(f"\n❌ 处理失败")

if __name__ == "__main__":
    main()
