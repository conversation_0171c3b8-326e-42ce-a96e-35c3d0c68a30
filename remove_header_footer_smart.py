#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新方案4：组合智能法删除PDF页眉页脚
分析PDF后自动选择最佳处理方法
"""

import os
import fitz  # PyMuPDF
import pdfplumber
import pypdf
from pypdf import PdfReader
import time

def analyze_pdf_structure(pdf_path):
    """
    深度分析PDF结构，确定最佳处理方法
    
    Returns:
        dict: 分析结果
    """
    
    print(f"🔍 开始深度分析PDF结构: {pdf_path}")
    
    analysis = {
        'total_pages': 0,
        'avg_page_size': (0, 0),
        'has_header': False,
        'has_footer': False,
        'header_height': 0,
        'footer_height': 0,
        'text_density': 0,
        'image_count': 0,
        'recommended_method': 'pymupdf_crop',
        'confidence': 0.5
    }
    
    try:
        # 使用PyMuPDF进行基础分析
        doc = fitz.open(pdf_path)
        analysis['total_pages'] = len(doc)
        
        # 分析页面尺寸
        page_sizes = []
        for page in doc:
            rect = page.rect
            page_sizes.append((rect.width, rect.height))
        
        if page_sizes:
            avg_width = sum(size[0] for size in page_sizes) / len(page_sizes)
            avg_height = sum(size[1] for size in page_sizes) / len(page_sizes)
            analysis['avg_page_size'] = (avg_width, avg_height)
        
        # 分析前5页的页眉页脚
        sample_pages = min(5, len(doc))
        header_detections = []
        footer_detections = []
        text_densities = []
        image_counts = []
        
        for page_num in range(sample_pages):
            page = doc[page_num]
            rect = page.rect
            
            # 获取文本信息
            text_dict = page.get_text("dict")
            
            # 计算文本密度
            text_blocks = len([block for block in text_dict["blocks"] if "lines" in block])
            text_densities.append(text_blocks)
            
            # 计算图像数量
            image_list = page.get_images()
            image_counts.append(len(image_list))
            
            # 分析页眉页脚
            header_threshold = rect.height * 0.15
            footer_threshold = rect.height * 0.85
            
            has_header_content = False
            has_footer_content = False
            header_y_min = rect.height
            footer_y_max = 0
            
            for block in text_dict["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            bbox = span["bbox"]
                            text = span["text"].strip()
                            
                            if text:
                                if bbox[1] > rect.height - header_threshold:
                                    has_header_content = True
                                    header_y_min = min(header_y_min, bbox[1])
                                
                                if bbox[3] < footer_threshold:
                                    has_footer_content = True
                                    footer_y_max = max(footer_y_max, bbox[3])
            
            header_detections.append(has_header_content)
            footer_detections.append(has_footer_content)
            
            if has_header_content:
                analysis['header_height'] = max(analysis['header_height'], 
                                               rect.height - header_y_min + 10)
            
            if has_footer_content:
                analysis['footer_height'] = max(analysis['footer_height'], 
                                               footer_y_max + 10)
        
        # 汇总分析结果
        analysis['has_header'] = sum(header_detections) >= sample_pages * 0.6
        analysis['has_footer'] = sum(footer_detections) >= sample_pages * 0.6
        analysis['text_density'] = sum(text_densities) / len(text_densities)
        analysis['image_count'] = sum(image_counts)
        
        doc.close()
        
        # 使用pdfplumber进行补充分析
        try:
            with pdfplumber.open(pdf_path) as pdf:
                # 检查文本提取质量
                sample_page = pdf.pages[0]
                extracted_text = sample_page.extract_text()
                
                if extracted_text and len(extracted_text.strip()) > 100:
                    analysis['text_extraction_quality'] = 'good'
                else:
                    analysis['text_extraction_quality'] = 'poor'
        except:
            analysis['text_extraction_quality'] = 'poor'
        
        # 决定推荐方法
        analysis = determine_best_method(analysis)
        
        print(f"📊 分析完成:")
        print(f"   总页数: {analysis['total_pages']}")
        print(f"   页面尺寸: {analysis['avg_page_size'][0]:.1f} x {analysis['avg_page_size'][1]:.1f}")
        print(f"   页眉检测: {'是' if analysis['has_header'] else '否'} (高度: {analysis['header_height']:.1f}pt)")
        print(f"   页脚检测: {'是' if analysis['has_footer'] else '否'} (高度: {analysis['footer_height']:.1f}pt)")
        print(f"   文本密度: {analysis['text_density']:.1f}")
        print(f"   图像数量: {analysis['image_count']}")
        print(f"   推荐方法: {analysis['recommended_method']} (置信度: {analysis['confidence']:.1f})")
        
        return analysis
        
    except Exception as e:
        print(f"❌ PDF分析失败: {e}")
        return analysis

def determine_best_method(analysis):
    """根据分析结果确定最佳方法"""
    
    confidence = 0.5
    method = 'pymupdf_crop'  # 默认方法
    
    # 规则1: 如果文件很大且页眉页脚简单，推荐裁剪法
    if analysis['total_pages'] > 100 and analysis['text_density'] > 10:
        if analysis['has_footer'] and not analysis['has_header']:
            method = 'pypdf_crop'
            confidence = 0.8
        elif analysis['has_header'] and analysis['has_footer']:
            method = 'pymupdf_crop'
            confidence = 0.7
    
    # 规则2: 如果有很多图像，推荐遮盖法
    if analysis['image_count'] > 20:
        method = 'pymupdf_redact'
        confidence = 0.7
    
    # 规则3: 如果文本提取质量好，推荐文本删除法
    if analysis.get('text_extraction_quality') == 'good' and analysis['total_pages'] < 50:
        method = 'pymupdf_text'
        confidence = 0.8
    
    # 规则4: 如果页面很小，推荐pdfplumber
    if analysis['avg_page_size'][1] < 600:
        method = 'pdfplumber'
        confidence = 0.6
    
    # 规则5: 如果只有页脚，推荐pypdf裁剪
    if analysis['has_footer'] and not analysis['has_header'] and analysis['footer_height'] < 60:
        method = 'pypdf_crop'
        confidence = 0.9
    
    analysis['recommended_method'] = method
    analysis['confidence'] = confidence
    
    return analysis

def execute_recommended_method(input_pdf, output_pdf, analysis):
    """执行推荐的方法"""
    
    method = analysis['recommended_method']
    print(f"🚀 执行推荐方法: {method}")
    
    start_time = time.time()
    success = False
    
    try:
        if method == 'pypdf_crop':
            from remove_header_footer_pypdf import remove_header_footer_pypdf_crop
            success = remove_header_footer_pypdf_crop(
                input_pdf, output_pdf,
                top_crop=analysis['header_height'],
                bottom_crop=analysis['footer_height']
            )
        
        elif method == 'pymupdf_crop':
            from remove_header_footer_crop import remove_header_footer_by_crop
            success = remove_header_footer_by_crop(
                input_pdf, output_pdf,
                top_margin=analysis['header_height'],
                bottom_margin=analysis['footer_height']
            )
        
        elif method == 'pymupdf_text':
            from remove_header_footer_text import remove_header_footer_by_text_detection
            success = remove_header_footer_by_text_detection(input_pdf, output_pdf)
        
        elif method == 'pymupdf_redact':
            from remove_header_footer_redact import remove_header_footer_smart_redaction
            success = remove_header_footer_smart_redaction(input_pdf, output_pdf)
        
        elif method == 'pdfplumber':
            from remove_header_footer_pdfplumber import remove_header_footer_pdfplumber
            success = remove_header_footer_pdfplumber(
                input_pdf, output_pdf,
                header_margin=analysis['header_height'],
                footer_margin=analysis['footer_height']
            )
        
        else:
            print(f"❌ 未知方法: {method}")
            return False
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if success:
            print(f"✅ 方法 {method} 执行成功！")
            print(f"⏱️  处理时间: {processing_time:.1f}秒")
        else:
            print(f"❌ 方法 {method} 执行失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 执行方法 {method} 时出错: {e}")
        return False

def smart_remove_header_footer(input_pdf, output_pdf):
    """智能删除页眉页脚主函数"""
    
    print("=" * 60)
    print("🧠 智能PDF页眉页脚删除工具")
    print("=" * 60)
    
    # 步骤1: 分析PDF
    analysis = analyze_pdf_structure(input_pdf)
    
    print("\n" + "="*60)
    
    # 步骤2: 执行推荐方法
    success = execute_recommended_method(input_pdf, output_pdf, analysis)
    
    if not success:
        print("\n🔄 推荐方法失败，尝试备用方法...")
        
        # 备用方法列表
        backup_methods = ['pypdf_crop', 'pymupdf_crop', 'pymupdf_text']
        
        for backup_method in backup_methods:
            if backup_method != analysis['recommended_method']:
                print(f"🔄 尝试备用方法: {backup_method}")
                
                analysis['recommended_method'] = backup_method
                success = execute_recommended_method(input_pdf, output_pdf, analysis)
                
                if success:
                    print(f"✅ 备用方法 {backup_method} 成功！")
                    break
    
    return success

def main():
    """主函数"""
    input_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    output_file = "08-19 C语言实践项目（正文）-改版_智能组合法.pdf"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误：找不到输入文件 {input_file}")
        return
    
    # 执行智能处理
    success = smart_remove_header_footer(input_file, output_file)
    
    if success:
        print(f"\n🎉 智能处理完成！输出文件: {output_file}")
    else:
        print(f"\n❌ 智能处理失败")

if __name__ == "__main__":
    main()
