#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新方案1：pdfplumber精确提取法删除PDF页眉页脚
使用pdfplumber库精确控制文本提取区域，重新生成PDF
"""

import pdfplumber
import os
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import io

def install_reportlab():
    """安装reportlab库"""
    try:
        import reportlab
        return True
    except ImportError:
        print("正在安装reportlab库...")
        os.system("pip install reportlab")
        try:
            import reportlab
            return True
        except ImportError:
            print("❌ reportlab安装失败")
            return False

def remove_header_footer_pdfplumber(input_pdf, output_pdf, 
                                   header_margin=50, footer_margin=50):
    """
    使用pdfplumber精确提取法删除页眉页脚
    
    Args:
        input_pdf (str): 输入PDF文件路径
        output_pdf (str): 输出PDF文件路径
        header_margin (float): 页眉边距（点）
        footer_margin (float): 页脚边距（点）
    """
    
    if not install_reportlab():
        return False
    
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    
    print(f"开始使用pdfplumber处理PDF文件: {input_pdf}")
    print(f"页眉边距: {header_margin}pt, 页脚边距: {footer_margin}pt")
    
    try:
        # 创建临时画布来生成新PDF
        packet = io.BytesIO()
        
        with pdfplumber.open(input_pdf) as pdf:
            total_pages = len(pdf.pages)
            print(f"总页数: {total_pages}")
            
            # 获取第一页尺寸作为参考
            first_page = pdf.pages[0]
            page_width = first_page.width
            page_height = first_page.height
            
            # 创建新PDF
            c = canvas.Canvas(packet, pagesize=(page_width, page_height))
            
            for page_num, page in enumerate(pdf.pages):
                # 显示进度
                if (page_num + 1) % 50 == 0 or page_num == 0:
                    print(f"处理进度: {page_num + 1}/{total_pages}")
                
                # 定义有效内容区域（去除页眉页脚）
                crop_box = (
                    0,                              # 左边界
                    footer_margin,                  # 底部边界（去除页脚）
                    page.width,                     # 右边界
                    page.height - header_margin     # 顶部边界（去除页眉）
                )
                
                # 裁剪页面
                cropped_page = page.crop(crop_box)
                
                # 提取裁剪后的文本
                text = cropped_page.extract_text()
                
                if text:
                    # 在新页面上重新绘制文本
                    c.setFont("Helvetica", 10)
                    
                    # 简单的文本重排（这里可以改进）
                    lines = text.split('\n')
                    y_position = page_height - header_margin - 20
                    
                    for line in lines:
                        if line.strip():
                            c.drawString(20, y_position, line.strip())
                            y_position -= 15
                            
                            # 如果超出页面，换页
                            if y_position < footer_margin + 20:
                                c.showPage()
                                y_position = page_height - header_margin - 20
                
                # 完成当前页
                c.showPage()
            
            # 保存PDF
            c.save()
        
        # 将生成的PDF写入文件
        packet.seek(0)
        with open(output_pdf, 'wb') as output_file:
            output_file.write(packet.read())
        
        print(f"✅ pdfplumber处理完成！输出文件: {output_pdf}")
        
        # 显示文件大小对比
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        print(f"文件大小对比:")
        print(f"  原文件: {original_size:.2f} MB")
        print(f"  新文件: {new_size:.2f} MB")
        print(f"  大小变化: {((new_size - original_size) / original_size * 100):+.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ pdfplumber处理失败: {e}")
        return False

def remove_header_footer_pdfplumber_advanced(input_pdf, output_pdf):
    """
    高级pdfplumber方法：智能检测页眉页脚
    """
    
    print(f"开始高级pdfplumber处理: {input_pdf}")
    
    try:
        with pdfplumber.open(input_pdf) as pdf:
            total_pages = len(pdf.pages)
            print(f"总页数: {total_pages}")
            
            # 分析前几页来确定页眉页脚位置
            header_areas = []
            footer_areas = []
            
            for page_num in range(min(5, total_pages)):
                page = pdf.pages[page_num]
                
                # 获取页面中的所有文本对象
                chars = page.chars
                
                if chars:
                    # 分析文本分布
                    y_positions = [char['y0'] for char in chars]
                    page_height = page.height
                    
                    # 检测页眉区域（顶部15%）
                    header_threshold = page_height * 0.85
                    header_chars = [char for char in chars if char['y0'] > header_threshold]
                    
                    # 检测页脚区域（底部15%）
                    footer_threshold = page_height * 0.15
                    footer_chars = [char for char in chars if char['y0'] < footer_threshold]
                    
                    if header_chars:
                        header_y = min(char['y0'] for char in header_chars)
                        header_areas.append(page_height - header_y)
                    
                    if footer_chars:
                        footer_y = max(char['y1'] for char in footer_chars)
                        footer_areas.append(footer_y)
            
            # 计算平均页眉页脚高度
            avg_header_margin = sum(header_areas) / len(header_areas) if header_areas else 50
            avg_footer_margin = sum(footer_areas) / len(footer_areas) if footer_areas else 50
            
            print(f"检测到的页眉边距: {avg_header_margin:.1f}pt")
            print(f"检测到的页脚边距: {avg_footer_margin:.1f}pt")
            
            # 使用检测到的边距进行处理
            return remove_header_footer_pdfplumber(
                input_pdf, output_pdf, 
                header_margin=avg_header_margin + 10,  # 增加一些缓冲
                footer_margin=avg_footer_margin + 10
            )
            
    except Exception as e:
        print(f"❌ 高级pdfplumber处理失败: {e}")
        return False

def main():
    """主函数"""
    input_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误：找不到输入文件 {input_file}")
        return
    
    print("=" * 60)
    print("PDF页眉页脚删除工具 - pdfplumber精确提取法")
    print("=" * 60)
    
    # 方法1：基础pdfplumber方法
    print("\n1. 基础pdfplumber方法:")
    output_file1 = "08-19 C语言实践项目（正文）-改版_pdfplumber基础.pdf"
    success1 = remove_header_footer_pdfplumber(
        input_pdf=input_file,
        output_pdf=output_file1,
        header_margin=40,
        footer_margin=50
    )
    
    if success1:
        print("\n" + "="*60)
        
        # 方法2：高级pdfplumber方法
        print("\n2. 高级pdfplumber方法（智能检测）:")
        output_file2 = "08-19 C语言实践项目（正文）-改版_pdfplumber高级.pdf"
        remove_header_footer_pdfplumber_advanced(input_file, output_file2)

if __name__ == "__main__":
    main()
