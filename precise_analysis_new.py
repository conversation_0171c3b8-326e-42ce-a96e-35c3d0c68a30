#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确分析新PDF文件 - 确定最佳裁剪参数
"""

import fitz  # PyMuPDF
import os

def analyze_precise_bounds(pdf_path):
    """精确分析页眉页脚边界"""
    
    print(f"🔍 精确分析PDF文件: {pdf_path}")
    print("="*80)
    
    doc = fitz.open(pdf_path)
    
    # 分析多个页面来确定精确边界
    sample_pages = [10, 20, 30, 50, 80, 100, 130]  # 分析有内容的页面
    sample_pages = [p for p in sample_pages if p < len(doc)]
    
    print(f"📊 总页数: {len(doc)}")
    print(f"🔍 分析页面: {sample_pages}")
    print()
    
    header_positions = []
    footer_positions = []
    content_top_positions = []
    content_bottom_positions = []
    
    for page_num in sample_pages:
        page = doc[page_num]
        rect = page.rect
        
        print(f"📄 第 {page_num + 1} 页详细分析:")
        print(f"   页面尺寸: {rect.width:.1f} x {rect.height:.1f}")
        
        # 获取所有文本块
        text_dict = page.get_text("dict")
        
        page_header_y = None
        page_footer_y = None
        content_texts = []
        
        for block in text_dict["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]
                            y_top = bbox[1]
                            y_bottom = bbox[3]
                            
                            # 判断是否为页眉
                            if y_top < 80 and any(keyword in text for keyword in ['项目', '动力电池', '新能源']):
                                page_header_y = y_bottom
                                print(f"   🔝 页眉: '{text}' - Y底部: {y_bottom:.1f}")
                            
                            # 判断是否为页脚（页码）
                            elif y_top > 680 and text.isdigit() and len(text) <= 4:
                                page_footer_y = y_top
                                print(f"   🔽 页脚: '{text}' - Y顶部: {y_top:.1f}")
                            
                            # 正文内容
                            elif 80 <= y_top <= 680:
                                content_texts.append({
                                    "text": text[:30] + "..." if len(text) > 30 else text,
                                    "y_top": y_top,
                                    "y_bottom": y_bottom
                                })
        
        # 分析正文内容的边界
        if content_texts:
            content_texts.sort(key=lambda x: x['y_top'], reverse=True)
            
            # 最顶部的正文
            top_content = content_texts[0]
            content_top_positions.append(top_content['y_top'])
            print(f"   📄 正文顶部: '{top_content['text']}' - Y: {top_content['y_top']:.1f}")
            
            # 最底部的正文
            bottom_content = content_texts[-1]
            content_bottom_positions.append(bottom_content['y_bottom'])
            print(f"   📄 正文底部: '{bottom_content['text']}' - Y: {bottom_content['y_bottom']:.1f}")
        
        if page_header_y:
            header_positions.append(page_header_y)
        if page_footer_y:
            footer_positions.append(page_footer_y)
        
        print()
    
    doc.close()
    
    # 计算建议的裁剪参数
    print("="*80)
    print("🎯 裁剪参数建议:")
    
    if header_positions:
        max_header_bottom = max(header_positions)
        suggested_header_crop = max_header_bottom + 10  # 增加10pt缓冲
        print(f"📏 页眉分析:")
        print(f"   页眉底部位置: {header_positions}")
        print(f"   最大页眉底部: {max_header_bottom:.1f}pt")
        print(f"   建议顶部裁剪: {suggested_header_crop:.1f}pt")
    else:
        suggested_header_crop = 0
        print(f"📏 未检测到页眉")
    
    if footer_positions and content_bottom_positions:
        min_footer_top = min(footer_positions)
        max_content_bottom = max(content_bottom_positions)
        
        # 确保不会裁剪到正文
        safe_footer_crop = 754 - max_content_bottom - 5  # 留5pt缓冲
        suggested_footer_crop = min(754 - min_footer_top + 10, safe_footer_crop)
        
        print(f"📏 页脚分析:")
        print(f"   页脚顶部位置: {footer_positions}")
        print(f"   最小页脚顶部: {min_footer_top:.1f}pt")
        print(f"   正文底部位置: {content_bottom_positions}")
        print(f"   最大正文底部: {max_content_bottom:.1f}pt")
        print(f"   安全裁剪距离: {safe_footer_crop:.1f}pt")
        print(f"   建议底部裁剪: {suggested_footer_crop:.1f}pt")
    else:
        suggested_footer_crop = 0
        print(f"📏 未检测到页脚或正文")
    
    print(f"\n🎯 最终建议:")
    print(f"   顶部裁剪: {suggested_header_crop:.0f}pt")
    print(f"   底部裁剪: {suggested_footer_crop:.0f}pt")
    
    return int(suggested_header_crop), int(suggested_footer_crop)

def main():
    """主函数"""
    input_file = "新能源汽车动力电池构造与检修（第二次正文）_未命名.pdf"
    
    if not os.path.exists(input_file):
        print(f"❌ 找不到文件: {input_file}")
        return
    
    header_crop, footer_crop = analyze_precise_bounds(input_file)
    
    print(f"\n💡 使用这些参数更新代码:")
    print(f"   header_crop = {header_crop}")
    print(f"   footer_crop = {footer_crop}")

if __name__ == "__main__":
    main()
