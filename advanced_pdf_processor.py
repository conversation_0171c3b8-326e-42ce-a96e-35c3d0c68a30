#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级PDF页眉页脚删除工具 - 集成所有8种方法
包含原有3种方法 + 新增5种方法
"""

import os
import sys
import time
from pathlib import Path

def show_advanced_menu():
    """显示高级菜单"""
    print("\n" + "="*80)
    print("🚀 高级PDF页眉页脚删除工具 - 8种方法全集")
    print("="*80)
    print("📋 原有方法:")
    print("1. PyMuPDF页面裁剪法 - 通过裁剪页面边距删除页眉页脚")
    print("2. PyMuPDF文本检测删除法 - 智能检测并删除页眉页脚文本")
    print("3. PyMuPDF区域遮盖法 - 用白色矩形遮盖页眉页脚区域")
    print()
    print("🆕 新增方法:")
    print("4. pdfplumber精确提取法 - 精确控制文本提取区域")
    print("5. pypdf现代处理法 - 使用最新pypdf库进行处理")
    print("6. 组合智能法 - AI分析后自动选择最佳方法")
    print("7. 简化AI重构法 - 基于文本重构的AI方法")
    print()
    print("🎯 批量处理:")
    print("8. 原有3种方法批量对比")
    print("9. 新增4种方法批量对比")
    print("10. 全部8种方法终极对比")
    print()
    print("0. 退出")
    print("="*80)

def get_user_choice():
    """获取用户选择"""
    while True:
        try:
            choice = input("请输入选项 (0-10): ").strip()
            if choice in [str(i) for i in range(11)]:
                return choice
            else:
                print("❌ 无效选项，请输入 0-10")
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            return '0'

def process_method_1(input_file):
    """方法1：PyMuPDF页面裁剪法"""
    try:
        from remove_header_footer_crop import remove_header_footer_by_crop
        output_file = input_file.replace('.pdf', '_PyMuPDF裁剪法.pdf')
        print(f"\n🔄 执行PyMuPDF页面裁剪法...")
        
        start_time = time.time()
        success = remove_header_footer_by_crop(
            input_pdf=input_file,
            output_pdf=output_file,
            top_margin=30,
            bottom_margin=40,
            left_margin=0,
            right_margin=0
        )
        end_time = time.time()
        
        return {
            'method': 'PyMuPDF页面裁剪法',
            'output_file': output_file,
            'success': success,
            'time': end_time - start_time
        }
    except Exception as e:
        print(f"❌ PyMuPDF页面裁剪法失败: {e}")
        return {'method': 'PyMuPDF页面裁剪法', 'success': False, 'error': str(e)}

def process_method_2(input_file):
    """方法2：PyMuPDF文本检测删除法"""
    try:
        from remove_header_footer_text import remove_header_footer_by_text_detection
        output_file = input_file.replace('.pdf', '_PyMuPDF文本删除法.pdf')
        print(f"\n🔄 执行PyMuPDF文本检测删除法...")
        
        start_time = time.time()
        remove_header_footer_by_text_detection(input_file, output_file)
        end_time = time.time()
        
        success = os.path.exists(output_file)
        return {
            'method': 'PyMuPDF文本检测删除法',
            'output_file': output_file,
            'success': success,
            'time': end_time - start_time
        }
    except Exception as e:
        print(f"❌ PyMuPDF文本检测删除法失败: {e}")
        return {'method': 'PyMuPDF文本检测删除法', 'success': False, 'error': str(e)}

def process_method_3(input_file):
    """方法3：PyMuPDF区域遮盖法"""
    try:
        from remove_header_footer_redact import remove_header_footer_smart_redaction
        output_file = input_file.replace('.pdf', '_PyMuPDF遮盖法.pdf')
        print(f"\n🔄 执行PyMuPDF区域遮盖法...")
        
        start_time = time.time()
        remove_header_footer_smart_redaction(input_file, output_file)
        end_time = time.time()
        
        success = os.path.exists(output_file)
        return {
            'method': 'PyMuPDF区域遮盖法',
            'output_file': output_file,
            'success': success,
            'time': end_time - start_time
        }
    except Exception as e:
        print(f"❌ PyMuPDF区域遮盖法失败: {e}")
        return {'method': 'PyMuPDF区域遮盖法', 'success': False, 'error': str(e)}

def process_method_4(input_file):
    """方法4：pdfplumber精确提取法"""
    try:
        from remove_header_footer_pdfplumber import remove_header_footer_pdfplumber_advanced
        output_file = input_file.replace('.pdf', '_pdfplumber精确法.pdf')
        print(f"\n🔄 执行pdfplumber精确提取法...")
        
        start_time = time.time()
        success = remove_header_footer_pdfplumber_advanced(input_file, output_file)
        end_time = time.time()
        
        return {
            'method': 'pdfplumber精确提取法',
            'output_file': output_file,
            'success': success,
            'time': end_time - start_time
        }
    except Exception as e:
        print(f"❌ pdfplumber精确提取法失败: {e}")
        return {'method': 'pdfplumber精确提取法', 'success': False, 'error': str(e)}

def process_method_5(input_file):
    """方法5：pypdf现代处理法"""
    try:
        from remove_header_footer_pypdf import remove_header_footer_pypdf_smart
        output_file = input_file.replace('.pdf', '_pypdf现代法.pdf')
        print(f"\n🔄 执行pypdf现代处理法...")
        
        start_time = time.time()
        success = remove_header_footer_pypdf_smart(input_file, output_file)
        end_time = time.time()
        
        return {
            'method': 'pypdf现代处理法',
            'output_file': output_file,
            'success': success,
            'time': end_time - start_time
        }
    except Exception as e:
        print(f"❌ pypdf现代处理法失败: {e}")
        return {'method': 'pypdf现代处理法', 'success': False, 'error': str(e)}

def process_method_6(input_file):
    """方法6：组合智能法"""
    try:
        from remove_header_footer_smart import smart_remove_header_footer
        output_file = input_file.replace('.pdf', '_组合智能法.pdf')
        print(f"\n🔄 执行组合智能法...")
        
        start_time = time.time()
        success = smart_remove_header_footer(input_file, output_file)
        end_time = time.time()
        
        return {
            'method': '组合智能法',
            'output_file': output_file,
            'success': success,
            'time': end_time - start_time
        }
    except Exception as e:
        print(f"❌ 组合智能法失败: {e}")
        return {'method': '组合智能法', 'success': False, 'error': str(e)}

def process_method_7(input_file):
    """方法7：简化AI重构法"""
    try:
        from remove_header_footer_marker import remove_header_footer_ai_simple
        output_file = input_file.replace('.pdf', '_简化AI法.pdf')
        print(f"\n🔄 执行简化AI重构法...")
        
        start_time = time.time()
        success = remove_header_footer_ai_simple(input_file, output_file)
        end_time = time.time()
        
        return {
            'method': '简化AI重构法',
            'output_file': output_file,
            'success': success,
            'time': end_time - start_time
        }
    except Exception as e:
        print(f"❌ 简化AI重构法失败: {e}")
        return {'method': '简化AI重构法', 'success': False, 'error': str(e)}

def batch_process_original(input_file):
    """批量处理：原有3种方法"""
    print(f"\n🔄 开始批量处理原有3种方法...")
    
    methods = [process_method_1, process_method_2, process_method_3]
    results = []
    
    for method_func in methods:
        result = method_func(input_file)
        results.append(result)
        print(f"{'✅' if result['success'] else '❌'} {result['method']}")
    
    return results

def batch_process_new(input_file):
    """批量处理：新增4种方法"""
    print(f"\n🔄 开始批量处理新增4种方法...")
    
    methods = [process_method_4, process_method_5, process_method_6, process_method_7]
    results = []
    
    for method_func in methods:
        result = method_func(input_file)
        results.append(result)
        print(f"{'✅' if result['success'] else '❌'} {result['method']}")
    
    return results

def batch_process_all(input_file):
    """批量处理：全部8种方法"""
    print(f"\n🔄 开始批量处理全部8种方法...")
    
    methods = [
        process_method_1, process_method_2, process_method_3, process_method_4,
        process_method_5, process_method_6, process_method_7
    ]
    results = []
    
    for i, method_func in enumerate(methods, 1):
        print(f"\n{'='*60}")
        print(f"执行方法 {i}/7")
        print(f"{'='*60}")
        
        result = method_func(input_file)
        results.append(result)
        
        if result['success']:
            print(f"✅ {result['method']} - 耗时: {result.get('time', 0):.1f}秒")
        else:
            print(f"❌ {result['method']} - 失败")
    
    return results

def show_results_summary(results, input_file):
    """显示结果汇总"""
    print(f"\n{'='*80}")
    print("📊 处理结果汇总")
    print(f"{'='*80}")
    
    original_size = os.path.getsize(input_file) / (1024 * 1024)
    print(f"📄 原文件: {input_file}")
    print(f"📊 原文件大小: {original_size:.2f} MB")
    print()
    
    print(f"{'方法':<20} {'状态':<8} {'文件大小(MB)':<12} {'大小变化':<10} {'耗时(秒)':<8}")
    print("-" * 80)
    
    for result in results:
        if result['success']:
            output_file = result['output_file']
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file) / (1024 * 1024)
                size_change = ((file_size - original_size) / original_size) * 100
                time_taken = result.get('time', 0)
                
                print(f"{result['method']:<20} {'✅成功':<8} {file_size:<12.2f} {size_change:+.1f}%{'':<6} {time_taken:<8.1f}")
            else:
                print(f"{result['method']:<20} {'❌失败':<8} {'N/A':<12} {'N/A':<10} {'N/A':<8}")
        else:
            print(f"{result['method']:<20} {'❌失败':<8} {'N/A':<12} {'N/A':<10} {'N/A':<8}")

def main():
    """主函数"""
    input_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"❌ 错误：找不到输入文件 {input_file}")
        return
    
    # 显示文件信息
    file_size = os.path.getsize(input_file) / (1024 * 1024)
    print(f"📄 输入文件: {input_file}")
    print(f"📊 文件大小: {file_size:.2f} MB")
    
    while True:
        show_advanced_menu()
        choice = get_user_choice()
        
        if choice == '0':
            print("👋 再见！")
            break
        elif choice == '1':
            result = process_method_1(input_file)
            show_results_summary([result], input_file)
        elif choice == '2':
            result = process_method_2(input_file)
            show_results_summary([result], input_file)
        elif choice == '3':
            result = process_method_3(input_file)
            show_results_summary([result], input_file)
        elif choice == '4':
            result = process_method_4(input_file)
            show_results_summary([result], input_file)
        elif choice == '5':
            result = process_method_5(input_file)
            show_results_summary([result], input_file)
        elif choice == '6':
            result = process_method_6(input_file)
            show_results_summary([result], input_file)
        elif choice == '7':
            result = process_method_7(input_file)
            show_results_summary([result], input_file)
        elif choice == '8':
            results = batch_process_original(input_file)
            show_results_summary(results, input_file)
        elif choice == '9':
            results = batch_process_new(input_file)
            show_results_summary(results, input_file)
        elif choice == '10':
            results = batch_process_all(input_file)
            show_results_summary(results, input_file)
        
        # 询问是否继续
        if choice != '10':  # 全部方法处理后不询问
            continue_choice = input("\n是否继续使用其他方法？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                print("👋 处理完成！")
                break

if __name__ == "__main__":
    main()
