#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新方案3：PDFtk命令行工具法删除PDF页眉页脚
使用PDFtk专业PDF工具包进行处理
"""

import os
import subprocess
import shutil
from pathlib import Path

def check_pdftk_available():
    """检查PDFtk是否可用"""
    try:
        result = subprocess.run(['pdftk', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ 检测到PDFtk: {result.stdout.strip()}")
            return True
        else:
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        return False

def install_pdftk_windows():
    """尝试在Windows上安装PDFtk"""
    print("正在尝试安装PDFtk...")
    
    # 检查是否有Chocolatey
    try:
        result = subprocess.run(['choco', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("使用Chocolatey安装PDFtk...")
            install_result = subprocess.run(['choco', 'install', 'pdftk-server', '-y'], 
                                          capture_output=True, text=True, timeout=300)
            if install_result.returncode == 0:
                print("✅ PDFtk安装成功")
                return True
            else:
                print(f"❌ PDFtk安装失败: {install_result.stderr}")
                return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    # 检查是否有Scoop
    try:
        result = subprocess.run(['scoop', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("使用Scoop安装PDFtk...")
            install_result = subprocess.run(['scoop', 'install', 'pdftk'], 
                                          capture_output=True, text=True, timeout=300)
            if install_result.returncode == 0:
                print("✅ PDFtk安装成功")
                return True
            else:
                print(f"❌ PDFtk安装失败: {install_result.stderr}")
                return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    print("❌ 无法自动安装PDFtk")
    print("请手动安装PDFtk:")
    print("1. 访问 https://www.pdflabs.com/tools/pdftk-the-pdf-toolkit/")
    print("2. 下载并安装PDFtk Server")
    print("3. 或使用包管理器: choco install pdftk-server")
    return False

def remove_header_footer_pdftk_crop(input_pdf, output_pdf, 
                                   top_margin=50, bottom_margin=50,
                                   left_margin=0, right_margin=0):
    """
    使用PDFtk裁剪方法删除页眉页脚
    
    Args:
        input_pdf (str): 输入PDF文件路径
        output_pdf (str): 输出PDF文件路径
        top_margin (float): 顶部边距（点）
        bottom_margin (float): 底部边距（点）
        left_margin (float): 左侧边距（点）
        right_margin (float): 右侧边距（点）
    """
    
    if not check_pdftk_available():
        print("❌ PDFtk不可用，尝试安装...")
        if not install_pdftk_windows():
            return False
        
        # 重新检查
        if not check_pdftk_available():
            print("❌ PDFtk安装后仍不可用")
            return False
    
    print(f"开始使用PDFtk处理PDF文件: {input_pdf}")
    print(f"裁剪参数 - 顶部: {top_margin}pt, 底部: {bottom_margin}pt")
    
    try:
        # 首先获取PDF信息
        info_cmd = ['pdftk', input_pdf, 'dump_data']
        info_result = subprocess.run(info_cmd, capture_output=True, text=True, timeout=30)
        
        if info_result.returncode != 0:
            print(f"❌ 获取PDF信息失败: {info_result.stderr}")
            return False
        
        # 解析页面数量
        total_pages = 0
        for line in info_result.stdout.split('\n'):
            if line.startswith('NumberOfPages:'):
                total_pages = int(line.split(':')[1].strip())
                break
        
        print(f"总页数: {total_pages}")
        
        # 创建临时目录
        temp_dir = Path("temp_pdftk")
        temp_dir.mkdir(exist_ok=True)
        
        try:
            # 分解PDF为单页
            burst_cmd = ['pdftk', input_pdf, 'burst', 'output', str(temp_dir / 'page_%02d.pdf')]
            burst_result = subprocess.run(burst_cmd, capture_output=True, text=True, timeout=60)
            
            if burst_result.returncode != 0:
                print(f"❌ PDF分解失败: {burst_result.stderr}")
                return False
            
            print("PDF分解完成，开始处理各页...")
            
            # 处理每一页（这里简化为直接合并，实际PDFtk的裁剪需要更复杂的操作）
            processed_pages = []
            
            for page_num in range(1, total_pages + 1):
                page_file = temp_dir / f'page_{page_num:02d}.pdf'
                if page_file.exists():
                    processed_pages.append(str(page_file))
                
                # 显示进度
                if page_num % 50 == 0 or page_num == 1:
                    print(f"处理进度: {page_num}/{total_pages}")
            
            # 合并处理后的页面
            if processed_pages:
                merge_cmd = ['pdftk'] + processed_pages + ['cat', 'output', output_pdf]
                merge_result = subprocess.run(merge_cmd, capture_output=True, text=True, timeout=120)
                
                if merge_result.returncode != 0:
                    print(f"❌ PDF合并失败: {merge_result.stderr}")
                    return False
                
                print(f"✅ PDFtk处理完成！输出文件: {output_pdf}")
                
                # 显示文件大小对比
                original_size = os.path.getsize(input_pdf) / (1024 * 1024)
                new_size = os.path.getsize(output_pdf) / (1024 * 1024)
                print(f"文件大小对比:")
                print(f"  原文件: {original_size:.2f} MB")
                print(f"  新文件: {new_size:.2f} MB")
                print(f"  大小变化: {((new_size - original_size) / original_size * 100):+.1f}%")
                
                return True
            else:
                print("❌ 没有找到处理后的页面")
                return False
                
        finally:
            # 清理临时文件
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
                
    except subprocess.TimeoutExpired:
        print("❌ PDFtk处理超时")
        return False
    except Exception as e:
        print(f"❌ PDFtk处理失败: {e}")
        return False

def remove_header_footer_pdftk_pages(input_pdf, output_pdf, 
                                    skip_first_pages=0, skip_last_pages=0):
    """
    使用PDFtk页面选择方法删除页眉页脚
    
    Args:
        input_pdf (str): 输入PDF文件路径
        output_pdf (str): 输出PDF文件路径
        skip_first_pages (int): 跳过前几页
        skip_last_pages (int): 跳过后几页
    """
    
    if not check_pdftk_available():
        print("❌ PDFtk不可用")
        return False
    
    print(f"开始使用PDFtk页面选择法处理: {input_pdf}")
    
    try:
        # 获取总页数
        info_cmd = ['pdftk', input_pdf, 'dump_data']
        info_result = subprocess.run(info_cmd, capture_output=True, text=True, timeout=30)
        
        if info_result.returncode != 0:
            print(f"❌ 获取PDF信息失败: {info_result.stderr}")
            return False
        
        total_pages = 0
        for line in info_result.stdout.split('\n'):
            if line.startswith('NumberOfPages:'):
                total_pages = int(line.split(':')[1].strip())
                break
        
        print(f"总页数: {total_pages}")
        
        # 计算要保留的页面范围
        start_page = skip_first_pages + 1
        end_page = total_pages - skip_last_pages
        
        if start_page > end_page:
            print("❌ 跳过的页面太多，没有剩余页面")
            return False
        
        print(f"保留页面范围: {start_page}-{end_page}")
        
        # 提取指定页面
        extract_cmd = ['pdftk', input_pdf, 'cat', f'{start_page}-{end_page}', 'output', output_pdf]
        extract_result = subprocess.run(extract_cmd, capture_output=True, text=True, timeout=60)
        
        if extract_result.returncode != 0:
            print(f"❌ 页面提取失败: {extract_result.stderr}")
            return False
        
        print(f"✅ PDFtk页面选择处理完成！输出文件: {output_pdf}")
        
        # 显示文件大小对比
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        print(f"文件大小对比:")
        print(f"  原文件: {original_size:.2f} MB")
        print(f"  新文件: {new_size:.2f} MB")
        print(f"  大小变化: {((new_size - original_size) / original_size * 100):+.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ PDFtk页面选择处理失败: {e}")
        return False

def main():
    """主函数"""
    input_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误：找不到输入文件 {input_file}")
        return
    
    print("=" * 60)
    print("PDF页眉页脚删除工具 - PDFtk命令行工具法")
    print("=" * 60)
    
    # 检查PDFtk可用性
    if not check_pdftk_available():
        print("PDFtk不可用，尝试安装...")
        if not install_pdftk_windows():
            print("❌ 无法使用PDFtk方法")
            return
    
    # 方法1：PDFtk裁剪法
    print("\n1. PDFtk裁剪法:")
    output_file1 = "08-19 C语言实践项目（正文）-改版_pdftk裁剪.pdf"
    success1 = remove_header_footer_pdftk_crop(
        input_pdf=input_file,
        output_pdf=output_file1,
        top_margin=30,
        bottom_margin=40
    )
    
    if success1:
        print("\n" + "="*60)
        
        # 方法2：PDFtk页面选择法
        print("\n2. PDFtk页面选择法:")
        output_file2 = "08-19 C语言实践项目（正文）-改版_pdftk页面.pdf"
        remove_header_footer_pdftk_pages(
            input_pdf=input_file,
            output_pdf=output_file2,
            skip_first_pages=0,
            skip_last_pages=0
        )

if __name__ == "__main__":
    main()
