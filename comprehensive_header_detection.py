#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面页眉检测 - 找出所有页眉模式
"""

import fitz  # PyMuPDF
import os
from collections import defaultdict

def comprehensive_header_detection(pdf_path):
    """
    全面检测所有可能的页眉模式
    """
    
    print(f"🔍 全面检测页眉模式: {pdf_path}")
    print("="*80)
    
    doc = fitz.open(pdf_path)
    total_pages = len(doc)
    
    # 检测更多页面
    sample_pages = list(range(0, min(100, total_pages), 3))  # 每隔2页检测一次
    
    print(f"📄 总页数: {total_pages}")
    print(f"🔍 检测页面: {len(sample_pages)} 页")
    
    # 收集顶部区域的所有文本
    top_texts = defaultdict(list)  # text -> [(page, y_pos, bbox)]
    
    for page_num in sample_pages:
        page = doc[page_num]
        rect = page.rect
        
        text_dict = page.get_text("dict")
        
        print(f"\n📄 第{page_num+1}页顶部文本:")
        
        for block in text_dict["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]
                            y_pos = bbox[1]
                            
                            # 检查顶部区域（扩大范围）
                            if y_pos < 150:  # 扩大到150pt
                                top_texts[text].append((page_num + 1, y_pos, bbox))
                                print(f"   Y:{y_pos:6.1f} - '{text}'")
    
    doc.close()
    
    print(f"\n📊 顶部文本统计:")
    print("="*80)
    
    # 分析出现频率
    frequent_texts = {}
    for text, occurrences in top_texts.items():
        if len(occurrences) >= 3:  # 出现3次以上的文本
            frequent_texts[text] = occurrences
    
    # 按出现次数排序
    sorted_texts = sorted(frequent_texts.items(), key=lambda x: len(x[1]), reverse=True)
    
    print(f"出现3次以上的顶部文本（可能的页眉）:")
    for text, occurrences in sorted_texts:
        print(f"\n📌 '{text}' - 出现{len(occurrences)}次")
        
        # 显示Y位置范围
        y_positions = [occ[1] for occ in occurrences]
        y_min, y_max = min(y_positions), max(y_positions)
        print(f"   Y位置范围: {y_min:.1f} - {y_max:.1f}")
        
        # 显示前5个出现位置
        for i, (page, y_pos, bbox) in enumerate(occurrences[:5]):
            print(f"   第{page}页 (Y:{y_pos:.1f})")
        if len(occurrences) > 5:
            print(f"   ... 还有{len(occurrences)-5}次")
    
    return frequent_texts

def create_complete_removal_script(frequent_texts):
    """
    基于检测结果创建完整的删除脚本
    """
    
    print(f"\n🎯 创建完整删除脚本")
    
    # 确定页眉文本
    confirmed_headers = set()
    for text, occurrences in frequent_texts.items():
        # 判断是否为页眉
        if (len(occurrences) >= 5 and  # 出现5次以上
            any(y_pos < 100 for _, y_pos, _ in occurrences)):  # 在顶部区域
            
            # 排除明显的正文内容
            if not is_likely_content(text):
                confirmed_headers.add(text)
                print(f"   ✓ 确认页眉: '{text}' (出现{len(occurrences)}次)")
    
    # 生成删除脚本
    script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整页眉页脚删除脚本 - 基于全面检测结果
"""

import fitz
import os

def complete_removal(input_pdf, output_pdf):
    """完整删除页眉页脚"""
    
    print(f"🎯 开始完整删除: {{input_pdf}}")
    
    # 确认的页眉文本
    confirmed_headers = {{
{chr(10).join(f"        '{header}'," for header in sorted(confirmed_headers))}
    }}
    
    # 项目标题模式
    project_patterns = ['项目一', '项目二', '项目三', '项目四', '项目五', '项目六', '项目七']
    
    try:
        doc = fitz.open(input_pdf)
        total_pages = len(doc)
        removed_count = 0
        
        for page_num in range(total_pages):
            if (page_num + 1) % 30 == 0 or page_num == 0:
                print(f"📈 处理进度: {{page_num + 1}}/{{total_pages}}")
            
            page = doc[page_num]
            text_dict = page.get_text("dict")
            areas_to_remove = []
            
            for block in text_dict["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"].strip()
                            if text:
                                bbox = span["bbox"]
                                y_pos = bbox[1]
                                
                                should_remove = False
                                
                                # 页眉检测（顶部150pt内）
                                if y_pos < 150:
                                    if (text in confirmed_headers or 
                                        text in project_patterns or
                                        text.startswith('项目')):
                                        should_remove = True
                                
                                # 页脚检测（底部80pt内）
                                elif y_pos > 674:
                                    if (text.isdigit() and 1 <= len(text) <= 3 and
                                        1 <= int(text) <= 200):
                                        should_remove = True
                                
                                if should_remove:
                                    areas_to_remove.append(bbox)
                                    removed_count += 1
            
            # 删除
            for bbox in areas_to_remove:
                rect_to_remove = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
                page.add_redact_annot(rect_to_remove, fill=(1, 1, 1))
            
            if areas_to_remove:
                page.apply_redactions()
        
        # 保存
        doc.save(output_pdf, garbage=4, deflate=True, clean=True)
        doc.close()
        
        print(f"✅ 完整删除完成！")
        print(f"📊 删除统计: {{removed_count}} 个元素")
        
        # 文件大小
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        size_change = ((new_size - original_size) / original_size) * 100
        
        print(f"📈 文件大小: {{original_size:.2f}} MB → {{new_size:.2f}} MB ({{size_change:+.1f}}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 删除失败: {{e}}")
        return False

def main():
    input_file = "新能源汽车动力电池构造与检修（第二次正文）_未命名.pdf"
    output_file = "新能源汽车动力电池构造与检修（第二次正文）_完整删除.pdf"
    
    if not os.path.exists(input_file):
        print(f"❌ 找不到文件: {{input_file}}")
        return
    
    success = complete_removal(input_file, output_file)
    
    if success:
        print(f"🎉 处理完成！")
        print(f"📁 输出文件: {{output_file}}")

if __name__ == "__main__":
    main()
'''
    
    # 保存脚本
    with open("complete_removal_script.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print(f"✅ 完整删除脚本已生成: complete_removal_script.py")

def is_likely_content(text):
    """判断是否可能是正文内容"""
    
    # 长文本通常是正文
    if len(text) > 50:
        return True
    
    # 包含常见正文词汇
    content_indicators = [
        '电池', '充电', '系统', '技术', '应用', '发展', '管理', '安全',
        '检修', '维护', '故障', '诊断', '性能', '容量', '电压', '电流'
    ]
    
    for indicator in content_indicators:
        if indicator in text and len(text) > 20:
            return True
    
    return False

def main():
    """主函数"""
    input_file = "新能源汽车动力电池构造与检修（第二次正文）_未命名.pdf"
    
    if not os.path.exists(input_file):
        print(f"❌ 找不到文件: {input_file}")
        return
    
    # 全面检测页眉
    frequent_texts = comprehensive_header_detection(input_file)
    
    # 创建完整删除脚本
    create_complete_removal_script(frequent_texts)

if __name__ == "__main__":
    main()
