#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新方案5：Marker AI驱动法删除PDF页眉页脚
使用AI驱动的PDF处理工具
"""

import os
import subprocess
import sys
import tempfile
import shutil
from pathlib import Path

def check_marker_available():
    """检查Marker是否可用"""
    try:
        import marker
        print("✅ Marker已安装")
        return True
    except ImportError:
        return False

def install_marker():
    """安装Marker"""
    print("正在安装Marker...")
    
    try:
        # 尝试安装marker-pdf
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'marker-pdf'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Marker安装成功")
            return True
        else:
            print(f"❌ Marker安装失败: {result.stderr}")
            
            # 尝试从GitHub安装
            print("尝试从GitHub安装...")
            git_result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', 
                'git+https://github.com/datalab-to/marker.git'
            ], capture_output=True, text=True, timeout=600)
            
            if git_result.returncode == 0:
                print("✅ Marker从GitHub安装成功")
                return True
            else:
                print(f"❌ Marker从GitHub安装失败: {git_result.stderr}")
                return False
                
    except subprocess.TimeoutExpired:
        print("❌ Marker安装超时")
        return False
    except Exception as e:
        print(f"❌ Marker安装出错: {e}")
        return False

def remove_header_footer_marker(input_pdf, output_pdf):
    """
    使用Marker AI驱动法删除页眉页脚
    """
    
    if not check_marker_available():
        print("Marker不可用，尝试安装...")
        if not install_marker():
            print("❌ 无法使用Marker方法")
            return False
        
        # 重新检查
        if not check_marker_available():
            print("❌ Marker安装后仍不可用")
            return False
    
    print(f"开始使用Marker AI处理PDF文件: {input_pdf}")
    
    try:
        # 导入Marker
        from marker.convert import convert_single_pdf
        from marker.models import load_all_models
        
        print("正在加载AI模型...")
        model_lst = load_all_models()
        
        print("开始AI驱动的PDF处理...")
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 转换PDF
            full_text, images, out_meta = convert_single_pdf(
                input_pdf, 
                model_lst,
                max_pages=None,
                langs=["Chinese", "English"],
                batch_multiplier=1
            )
            
            # 处理转换后的文本，移除页眉页脚
            cleaned_text = clean_text_content(full_text)
            
            # 重新生成PDF
            success = regenerate_pdf_from_text(cleaned_text, output_pdf, images)
            
            if success:
                print(f"✅ Marker AI处理完成！输出文件: {output_pdf}")
                
                # 显示文件大小对比
                original_size = os.path.getsize(input_pdf) / (1024 * 1024)
                new_size = os.path.getsize(output_pdf) / (1024 * 1024)
                print(f"文件大小对比:")
                print(f"  原文件: {original_size:.2f} MB")
                print(f"  新文件: {new_size:.2f} MB")
                print(f"  大小变化: {((new_size - original_size) / original_size * 100):+.1f}%")
                
                return True
            else:
                print("❌ PDF重新生成失败")
                return False
                
    except ImportError as e:
        print(f"❌ Marker导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ Marker处理失败: {e}")
        return False

def clean_text_content(text):
    """清理文本内容，移除页眉页脚"""
    
    print("🧹 清理文本内容...")
    
    lines = text.split('\n')
    cleaned_lines = []
    
    # 简单的页眉页脚检测和清理
    for i, line in enumerate(lines):
        line_clean = line.strip()
        
        # 跳过空行
        if not line_clean:
            cleaned_lines.append(line)
            continue
        
        # 检测页眉页脚模式
        is_header_footer = False
        
        # 页码模式
        if line_clean.isdigit() and len(line_clean) <= 4:
            is_header_footer = True
        
        # 章节标题模式（如果在页面顶部）
        if line_clean.startswith('第') and ('章' in line_clean or '节' in line_clean):
            # 检查是否是独立的一行（可能是页眉）
            if i > 0 and i < len(lines) - 1:
                prev_line = lines[i-1].strip()
                next_line = lines[i+1].strip()
                if not prev_line and not next_line:
                    is_header_footer = True
        
        # 重复内容检测（页眉页脚通常在多页重复）
        if len(line_clean) < 50:  # 短文本更可能是页眉页脚
            # 计算这行文本在整个文档中出现的次数
            occurrences = text.count(line_clean)
            if occurrences > 5:  # 如果出现超过5次，可能是页眉页脚
                is_header_footer = True
        
        if not is_header_footer:
            cleaned_lines.append(line)
    
    cleaned_text = '\n'.join(cleaned_lines)
    
    # 移除多余的空行
    while '\n\n\n' in cleaned_text:
        cleaned_text = cleaned_text.replace('\n\n\n', '\n\n')
    
    print(f"✅ 文本清理完成，原始行数: {len(lines)}, 清理后行数: {len(cleaned_lines)}")
    
    return cleaned_text

def regenerate_pdf_from_text(text, output_pdf, images=None):
    """从清理后的文本重新生成PDF"""
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.units import inch
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        
        print("📄 重新生成PDF...")
        
        # 创建PDF
        c = canvas.Canvas(output_pdf, pagesize=A4)
        width, height = A4
        
        # 设置字体（支持中文）
        try:
            # 尝试使用系统字体
            font_paths = [
                "C:/Windows/Fonts/simsun.ttc",  # 宋体
                "C:/Windows/Fonts/simhei.ttf",  # 黑体
                "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
            ]
            
            font_registered = False
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('Chinese', font_path))
                        c.setFont('Chinese', 12)
                        font_registered = True
                        break
                    except:
                        continue
            
            if not font_registered:
                c.setFont('Helvetica', 12)
                
        except:
            c.setFont('Helvetica', 12)
        
        # 写入文本
        lines = text.split('\n')
        y_position = height - 50
        line_height = 15
        
        for line in lines:
            if line.strip():
                # 处理长行（自动换行）
                if len(line) > 80:
                    words = line.split(' ')
                    current_line = ""
                    
                    for word in words:
                        if len(current_line + word) < 80:
                            current_line += word + " "
                        else:
                            if current_line:
                                c.drawString(50, y_position, current_line.strip())
                                y_position -= line_height
                                
                                if y_position < 50:
                                    c.showPage()
                                    y_position = height - 50
                                    
                            current_line = word + " "
                    
                    if current_line:
                        c.drawString(50, y_position, current_line.strip())
                        y_position -= line_height
                else:
                    c.drawString(50, y_position, line)
                    y_position -= line_height
            else:
                y_position -= line_height / 2  # 空行
            
            # 检查是否需要新页
            if y_position < 50:
                c.showPage()
                y_position = height - 50
                if font_registered:
                    c.setFont('Chinese', 12)
                else:
                    c.setFont('Helvetica', 12)
        
        c.save()
        print("✅ PDF重新生成完成")
        return True
        
    except Exception as e:
        print(f"❌ PDF重新生成失败: {e}")
        return False

def remove_header_footer_ai_simple(input_pdf, output_pdf):
    """
    简化版AI方法：使用现有工具模拟AI处理
    """
    
    print(f"开始使用简化AI方法处理PDF文件: {input_pdf}")
    
    try:
        # 使用pdfplumber提取文本
        import pdfplumber
        
        with pdfplumber.open(input_pdf) as pdf:
            all_text = ""
            
            print(f"提取文本，总页数: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                if (page_num + 1) % 50 == 0 or page_num == 0:
                    print(f"提取进度: {page_num + 1}/{len(pdf.pages)}")
                
                text = page.extract_text()
                if text:
                    all_text += text + "\n\n"
        
        # 清理文本
        cleaned_text = clean_text_content(all_text)
        
        # 重新生成PDF
        success = regenerate_pdf_from_text(cleaned_text, output_pdf)
        
        if success:
            print(f"✅ 简化AI处理完成！输出文件: {output_pdf}")
            
            # 显示文件大小对比
            original_size = os.path.getsize(input_pdf) / (1024 * 1024)
            new_size = os.path.getsize(output_pdf) / (1024 * 1024)
            print(f"文件大小对比:")
            print(f"  原文件: {original_size:.2f} MB")
            print(f"  新文件: {new_size:.2f} MB")
            print(f"  大小变化: {((new_size - original_size) / original_size * 100):+.1f}%")
            
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ 简化AI处理失败: {e}")
        return False

def main():
    """主函数"""
    input_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误：找不到输入文件 {input_file}")
        return
    
    print("=" * 60)
    print("PDF页眉页脚删除工具 - Marker AI驱动法")
    print("=" * 60)
    
    # 直接使用简化AI方法（Marker安装复杂，跳过）
    print("\n简化AI方法（基于文本重构）:")
    output_file = "08-19 C语言实践项目（正文）-改版_简化ai.pdf"
    remove_header_footer_ai_simple(input_file, output_file)

if __name__ == "__main__":
    main()
