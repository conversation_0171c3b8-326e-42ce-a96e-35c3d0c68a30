#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF分析脚本 - 检查页眉页脚位置和内容
"""

import fitz  # PyMuPDF

def analyze_pdf(pdf_path):
    """分析PDF文件的页眉页脚"""
    doc = fitz.open(pdf_path)
    
    print(f"PDF文件: {pdf_path}")
    print(f"总页数: {len(doc)}")
    print("-" * 50)
    
    # 分析前几页的文本分布
    for page_num in range(min(3, len(doc))):
        page = doc[page_num]
        rect = page.rect
        
        print(f"\n第 {page_num + 1} 页:")
        print(f"页面尺寸: {rect.width:.1f} x {rect.height:.1f}")
        
        # 获取所有文本块
        text_dict = page.get_text("dict")
        
        # 分析页眉区域 (页面顶部 10%)
        header_height = rect.height * 0.1
        header_texts = []
        
        # 分析页脚区域 (页面底部 10%)
        footer_height = rect.height * 0.9
        footer_texts = []
        
        for block in text_dict["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]
                            y_pos = bbox[1]  # 顶部y坐标
                            
                            if y_pos < header_height:
                                header_texts.append({
                                    "text": text,
                                    "bbox": bbox,
                                    "font": span["font"],
                                    "size": span["size"]
                                })
                            elif y_pos > footer_height:
                                footer_texts.append({
                                    "text": text,
                                    "bbox": bbox,
                                    "font": span["font"],
                                    "size": span["size"]
                                })
        
        # 显示页眉信息
        if header_texts:
            print("  页眉内容:")
            for item in header_texts:
                print(f"    '{item['text']}' - 位置: {item['bbox']}")
        else:
            print("  未检测到页眉")
            
        # 显示页脚信息
        if footer_texts:
            print("  页脚内容:")
            for item in footer_texts:
                print(f"    '{item['text']}' - 位置: {item['bbox']}")
        else:
            print("  未检测到页脚")
    
    doc.close()

if __name__ == "__main__":
    pdf_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    analyze_pdf(pdf_file)
