#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方案3：区域遮盖法删除PDF页眉页脚
使用白色矩形遮盖页眉页脚区域
"""

import fitz  # PyMuPDF
import os

def remove_header_footer_by_redaction(input_pdf, output_pdf, 
                                     header_height=50, footer_height=50):
    """
    通过区域遮盖删除页眉页脚
    
    Args:
        input_pdf (str): 输入PDF文件路径
        output_pdf (str): 输出PDF文件路径
        header_height (float): 页眉区域高度（像素）
        footer_height (float): 页脚区域高度（像素）
    """
    
    print(f"开始处理PDF文件: {input_pdf}")
    print(f"遮盖参数 - 页眉高度: {header_height}px, 页脚高度: {footer_height}px")
    
    # 打开PDF文件
    doc = fitz.open(input_pdf)
    total_pages = len(doc)
    print(f"总页数: {total_pages}")
    
    redacted_areas = 0
    
    try:
        for page_num in range(total_pages):
            # 显示进度
            if (page_num + 1) % 50 == 0 or page_num == 0:
                print(f"处理进度: {page_num + 1}/{total_pages}")
            
            page = doc[page_num]
            page_rect = page.rect
            
            # 定义页眉区域矩形
            if header_height > 0:
                header_rect = fitz.Rect(
                    page_rect.x0,                    # 左边界
                    page_rect.y0,                    # 顶部
                    page_rect.x1,                    # 右边界
                    page_rect.y0 + header_height     # 页眉底部
                )
                
                # 添加页眉遮盖区域
                page.add_redact_annot(header_rect, fill=(1, 1, 1))  # 白色填充
                redacted_areas += 1
            
            # 定义页脚区域矩形
            if footer_height > 0:
                footer_rect = fitz.Rect(
                    page_rect.x0,                    # 左边界
                    page_rect.y1 - footer_height,    # 页脚顶部
                    page_rect.x1,                    # 右边界
                    page_rect.y1                     # 底部
                )
                
                # 添加页脚遮盖区域
                page.add_redact_annot(footer_rect, fill=(1, 1, 1))  # 白色填充
                redacted_areas += 1
            
            # 应用遮盖
            page.apply_redactions()
        
        # 保存修改后的PDF
        doc.save(output_pdf)
        print(f"✅ 区域遮盖完成！输出文件: {output_pdf}")
        print(f"总共遮盖了 {redacted_areas} 个区域")
        
        # 显示文件大小对比
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        print(f"文件大小对比:")
        print(f"  原文件: {original_size:.2f} MB")
        print(f"  新文件: {new_size:.2f} MB")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
    finally:
        doc.close()

def remove_header_footer_smart_redaction(input_pdf, output_pdf):
    """
    智能区域遮盖：先检测内容再遮盖
    
    Args:
        input_pdf (str): 输入PDF文件路径
        output_pdf (str): 输出PDF文件路径
    """
    
    print(f"开始智能处理PDF文件: {input_pdf}")
    
    # 打开PDF文件
    doc = fitz.open(input_pdf)
    total_pages = len(doc)
    print(f"总页数: {total_pages}")
    
    redacted_areas = 0
    
    try:
        for page_num in range(total_pages):
            # 显示进度
            if (page_num + 1) % 50 == 0 or page_num == 0:
                print(f"处理进度: {page_num + 1}/{total_pages}")
            
            page = doc[page_num]
            page_rect = page.rect
            
            # 获取页面文本信息
            text_dict = page.get_text("dict")
            
            # 分析页眉页脚区域
            header_threshold = page_rect.height * 0.15  # 顶部15%
            footer_threshold = page_rect.height * 0.85  # 底部15%
            
            header_areas = []
            footer_areas = []
            
            # 检测页眉页脚文本位置
            for block in text_dict["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            bbox = span["bbox"]
                            text = span["text"].strip()
                            
                            if text:  # 非空文本
                                if bbox[1] < header_threshold:  # 在页眉区域
                                    header_areas.append(bbox)
                                elif bbox[3] > footer_threshold:  # 在页脚区域
                                    footer_areas.append(bbox)
            
            # 遮盖页眉区域
            if header_areas:
                # 计算页眉区域的边界
                min_x = min(area[0] for area in header_areas) - 5
                min_y = min(area[1] for area in header_areas) - 5
                max_x = max(area[2] for area in header_areas) + 5
                max_y = max(area[3] for area in header_areas) + 5
                
                header_rect = fitz.Rect(min_x, min_y, max_x, max_y)
                page.add_redact_annot(header_rect, fill=(1, 1, 1))
                redacted_areas += 1
            
            # 遮盖页脚区域
            if footer_areas:
                # 计算页脚区域的边界
                min_x = min(area[0] for area in footer_areas) - 5
                min_y = min(area[1] for area in footer_areas) - 5
                max_x = max(area[2] for area in footer_areas) + 5
                max_y = max(area[3] for area in footer_areas) + 5
                
                footer_rect = fitz.Rect(min_x, min_y, max_x, max_y)
                page.add_redact_annot(footer_rect, fill=(1, 1, 1))
                redacted_areas += 1
            
            # 应用遮盖
            page.apply_redactions()
        
        # 保存修改后的PDF
        doc.save(output_pdf)
        print(f"✅ 智能遮盖完成！输出文件: {output_pdf}")
        print(f"总共遮盖了 {redacted_areas} 个区域")
        
        # 显示文件大小对比
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        print(f"文件大小对比:")
        print(f"  原文件: {original_size:.2f} MB")
        print(f"  新文件: {new_size:.2f} MB")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
    finally:
        doc.close()

def main():
    """主函数"""
    input_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误：找不到输入文件 {input_file}")
        return
    
    print("=" * 60)
    print("PDF页眉页脚删除工具 - 区域遮盖法")
    print("=" * 60)
    
    # 方法1：固定区域遮盖
    print("\n1. 固定区域遮盖法:")
    output_file1 = "08-19 C语言实践项目（正文）-改版_固定遮盖法.pdf"
    remove_header_footer_by_redaction(
        input_pdf=input_file,
        output_pdf=output_file1,
        header_height=30,   # 页眉30像素
        footer_height=40    # 页脚40像素
    )
    
    print("\n" + "="*60)
    
    # 方法2：智能区域遮盖
    print("\n2. 智能区域遮盖法:")
    output_file2 = "08-19 C语言实践项目（正文）-改版_智能遮盖法.pdf"
    remove_header_footer_smart_redaction(input_file, output_file2)

if __name__ == "__main__":
    main()
