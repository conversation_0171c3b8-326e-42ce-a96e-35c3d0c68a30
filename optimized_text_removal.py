#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的文本删除方案 - 轻量级删除，控制文件大小
"""

import fitz  # PyMuPDF
import os
import re

def analyze_header_footer_patterns(pdf_path, sample_ratio=0.3):
    """
    分析页眉页脚模式，使用采样方式提高效率
    
    Args:
        pdf_path (str): PDF文件路径
        sample_ratio (float): 采样比例
    """
    
    print(f"🔍 分析页眉页脚模式: {pdf_path}")
    
    doc = fitz.open(pdf_path)
    total_pages = len(doc)
    
    # 计算采样页面
    sample_count = max(20, int(total_pages * sample_ratio))
    step = max(1, total_pages // sample_count)
    sample_pages = list(range(0, total_pages, step))
    
    print(f"📄 总页数: {total_pages}")
    print(f"🔍 采样页面: {len(sample_pages)} 页")
    
    header_texts = set()
    footer_texts = set()
    header_y_positions = []
    footer_y_positions = []
    
    for page_num in sample_pages:
        page = doc[page_num]
        rect = page.rect
        
        text_dict = page.get_text("dict")
        
        for block in text_dict["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]
                            y_pos = bbox[1]
                            
                            # 页眉检测（顶部20%）
                            if y_pos < rect.height * 0.2:
                                if is_header_pattern(text):
                                    header_texts.add(text)
                                    header_y_positions.append(y_pos)
                            
                            # 页脚检测（底部20%）
                            elif y_pos > rect.height * 0.8:
                                if is_footer_pattern(text, page_num):
                                    footer_texts.add(text)
                                    footer_y_positions.append(y_pos)
    
    doc.close()
    
    # 分析结果
    result = {
        "header_texts": header_texts,
        "footer_texts": footer_texts,
        "header_y_range": (min(header_y_positions), max(header_y_positions)) if header_y_positions else None,
        "footer_y_range": (min(footer_y_positions), max(footer_y_positions)) if footer_y_positions else None
    }
    
    print(f"📊 分析结果:")
    print(f"   页眉模式: {len(header_texts)} 种")
    if header_texts:
        print(f"   页眉示例: {list(header_texts)[:5]}")
        print(f"   页眉Y范围: {result['header_y_range']}")
    
    print(f"   页脚模式: {len(footer_texts)} 种")
    if footer_texts:
        print(f"   页脚示例: {list(footer_texts)[:5]}")
        print(f"   页脚Y范围: {result['footer_y_range']}")
    
    return result

def is_header_pattern(text):
    """判断是否为页眉模式"""
    
    text_clean = text.strip()
    
    # 项目标题
    if re.match(r'^项目[一二三四五六七八九十\d]+', text_clean):
        return True
    
    # 常见页眉内容
    header_keywords = [
        '动力电池的发展历程', '动力电池安全与防护措施', '动力电池基本知识',
        '动力电池管理系统检修', '动力电池维护与检查', '动力电池故障诊断',
        '动力电池充电系统检修'
    ]
    
    if text_clean in header_keywords:
        return True
    
    return False

def is_footer_pattern(text, page_num):
    """判断是否为页脚模式"""
    
    text_clean = text.strip()
    
    # 纯数字页码
    if text_clean.isdigit() and 1 <= len(text_clean) <= 4:
        try:
            page_number = int(text_clean)
            if 1 <= page_number <= 500:
                return True
        except:
            pass
    
    return False

def lightweight_text_removal(input_pdf, output_pdf, patterns):
    """
    轻量级文本删除，最小化文件大小影响
    """
    
    print(f"\n🎯 开始轻量级文本删除")
    print("策略: 精确删除，最小化文件大小影响")
    
    try:
        doc = fitz.open(input_pdf)
        total_pages = len(doc)
        removed_count = 0
        
        for page_num in range(total_pages):
            if (page_num + 1) % 30 == 0 or page_num == 0:
                print(f"📈 处理进度: {page_num + 1}/{total_pages}")
            
            page = doc[page_num]
            rect = page.rect
            
            text_dict = page.get_text("dict")
            areas_to_remove = []
            
            for block in text_dict["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"].strip()
                            if text:
                                bbox = span["bbox"]
                                y_pos = bbox[1]
                                
                                should_remove = False
                                
                                # 基于模式匹配的精确判断
                                if patterns["header_y_range"] and \
                                   patterns["header_y_range"][0] <= y_pos <= patterns["header_y_range"][1]:
                                    if text in patterns["header_texts"] or is_header_pattern(text):
                                        should_remove = True
                                
                                elif patterns["footer_y_range"] and \
                                     patterns["footer_y_range"][0] <= y_pos <= patterns["footer_y_range"][1]:
                                    if text in patterns["footer_texts"] or is_footer_pattern(text, page_num):
                                        should_remove = True
                                
                                if should_remove:
                                    areas_to_remove.append(bbox)
                                    removed_count += 1
            
            # 使用最小的redact区域
            for bbox in areas_to_remove:
                rect_to_remove = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
                page.add_redact_annot(rect_to_remove, fill=(1, 1, 1))
            
            # 只在有删除内容时才应用
            if areas_to_remove:
                page.apply_redactions()
        
        # 保存时使用压缩选项
        doc.save(output_pdf, garbage=4, deflate=True, clean=True)
        doc.close()
        
        print(f"\n✅ 轻量级删除完成！")
        print(f"📊 删除统计: {removed_count} 个文本元素")
        
        # 文件大小对比
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        size_change = ((new_size - original_size) / original_size) * 100
        
        print(f"📈 文件大小对比:")
        print(f"   原文件: {original_size:.2f} MB")
        print(f"   新文件: {new_size:.2f} MB")
        print(f"   大小变化: {size_change:+.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 删除失败: {e}")
        return False

def create_minimal_crop_version(input_pdf, output_pdf):
    """
    创建最小裁剪版本作为对比
    """
    
    print(f"\n✂️ 创建最小裁剪版本（对比用）")
    
    try:
        doc = fitz.open(input_pdf)
        new_doc = fitz.open()
        
        total_pages = len(doc)
        
        for page_num in range(total_pages):
            page = doc[page_num]
            original_rect = page.rect
            
            # 最小裁剪：只裁剪页眉页脚
            new_rect = fitz.Rect(
                original_rect.x0,           # 左边界不变
                original_rect.y0 + 70,      # 顶部裁剪70pt
                original_rect.x1,           # 右边界不变
                original_rect.y1 - 60       # 底部裁剪60pt
            )
            
            # 创建新页面
            new_page = new_doc.new_page(width=new_rect.width, height=new_rect.height)
            
            # 复制内容
            new_page.show_pdf_page(new_page.rect, doc, page_num, clip=new_rect)
        
        # 保存
        new_doc.save(output_pdf)
        new_doc.close()
        doc.close()
        
        # 文件大小
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        size_change = ((new_size - original_size) / original_size) * 100
        
        print(f"✅ 最小裁剪版本完成")
        print(f"📈 文件大小: {original_size:.2f} MB → {new_size:.2f} MB ({size_change:+.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 裁剪失败: {e}")
        return False

def main():
    """主函数"""
    input_file = "新能源汽车动力电池构造与检修（第二次正文）_未命名.pdf"
    
    if not os.path.exists(input_file):
        print(f"❌ 找不到文件: {input_file}")
        return
    
    print("🎯 优化的页眉页脚删除工具")
    print("="*60)
    print("提供两种方案:")
    print("1. 轻量级文本删除 - 保持页面完整，控制文件大小")
    print("2. 最小裁剪版本 - 作为对比参考")
    print("="*60)
    
    # 分析页眉页脚模式
    patterns = analyze_header_footer_patterns(input_file)
    
    # 方案1: 轻量级文本删除
    output_file1 = "新能源汽车动力电池构造与检修（第二次正文）_轻量级删除.pdf"
    success1 = lightweight_text_removal(input_file, output_file1, patterns)
    
    # 方案2: 最小裁剪版本
    output_file2 = "新能源汽车动力电池构造与检修（第二次正文）_最小裁剪对比.pdf"
    success2 = create_minimal_crop_version(input_file, output_file2)
    
    print(f"\n🎉 处理完成！")
    print(f"📁 方案1 (轻量级删除): {output_file1}")
    print(f"📁 方案2 (最小裁剪): {output_file2}")
    print(f"💡 建议对比两个版本，选择最适合的方案")

if __name__ == "__main__":
    main()
