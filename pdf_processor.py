#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF页眉页脚删除工具 - 主控制脚本
集成三种删除方法，用户可选择使用
"""

import os
import sys
from pathlib import Path

# 导入三种方法的模块
try:
    from remove_header_footer_crop import remove_header_footer_by_crop
    from remove_header_footer_text import remove_header_footer_by_text_detection
    from remove_header_footer_redact import (
        remove_header_footer_by_redaction, 
        remove_header_footer_smart_redaction
    )
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保所有脚本文件都在同一目录下")
    sys.exit(1)

def show_menu():
    """显示菜单"""
    print("\n" + "="*60)
    print("PDF页眉页脚删除工具")
    print("="*60)
    print("请选择删除方法:")
    print("1. 页面裁剪法 - 通过裁剪页面边距删除页眉页脚")
    print("2. 文本检测删除法 - 智能检测并删除页眉页脚文本")
    print("3. 固定区域遮盖法 - 用白色矩形遮盖固定区域")
    print("4. 智能区域遮盖法 - 检测内容后精确遮盖")
    print("5. 批量处理 - 使用所有方法生成对比文件")
    print("0. 退出")
    print("="*60)

def get_user_choice():
    """获取用户选择"""
    while True:
        try:
            choice = input("请输入选项 (0-5): ").strip()
            if choice in ['0', '1', '2', '3', '4', '5']:
                return choice
            else:
                print("❌ 无效选项，请输入 0-5")
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            return '0'

def process_method_1(input_file):
    """方法1：页面裁剪法"""
    output_file = input_file.replace('.pdf', '_裁剪法.pdf')
    print(f"\n🔄 使用页面裁剪法处理...")
    
    remove_header_footer_by_crop(
        input_pdf=input_file,
        output_pdf=output_file,
        top_margin=30,
        bottom_margin=40,
        left_margin=0,
        right_margin=0
    )
    return output_file

def process_method_2(input_file):
    """方法2：文本检测删除法"""
    output_file = input_file.replace('.pdf', '_文本删除法.pdf')
    print(f"\n🔄 使用文本检测删除法处理...")
    
    remove_header_footer_by_text_detection(input_file, output_file)
    return output_file

def process_method_3(input_file):
    """方法3：固定区域遮盖法"""
    output_file = input_file.replace('.pdf', '_固定遮盖法.pdf')
    print(f"\n🔄 使用固定区域遮盖法处理...")
    
    remove_header_footer_by_redaction(
        input_pdf=input_file,
        output_pdf=output_file,
        header_height=30,
        footer_height=40
    )
    return output_file

def process_method_4(input_file):
    """方法4：智能区域遮盖法"""
    output_file = input_file.replace('.pdf', '_智能遮盖法.pdf')
    print(f"\n🔄 使用智能区域遮盖法处理...")
    
    remove_header_footer_smart_redaction(input_file, output_file)
    return output_file

def process_batch(input_file):
    """批量处理：使用所有方法"""
    print(f"\n🔄 开始批量处理，将使用所有4种方法...")
    
    results = []
    methods = [
        ("页面裁剪法", process_method_1),
        ("文本检测删除法", process_method_2),
        ("固定区域遮盖法", process_method_3),
        ("智能区域遮盖法", process_method_4)
    ]
    
    for method_name, method_func in methods:
        try:
            print(f"\n{'='*40}")
            print(f"正在执行: {method_name}")
            print(f"{'='*40}")
            
            output_file = method_func(input_file)
            results.append((method_name, output_file, "✅ 成功"))
            
        except Exception as e:
            print(f"❌ {method_name} 处理失败: {e}")
            results.append((method_name, "无", f"❌ 失败: {e}"))
    
    # 显示批量处理结果
    print(f"\n{'='*60}")
    print("批量处理结果汇总:")
    print(f"{'='*60}")
    for method_name, output_file, status in results:
        print(f"{method_name:15} -> {output_file:30} {status}")
    
    return results

def main():
    """主函数"""
    input_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"❌ 错误：找不到输入文件 {input_file}")
        print("请确保PDF文件在当前目录下")
        return
    
    # 显示文件信息
    file_size = os.path.getsize(input_file) / (1024 * 1024)
    print(f"📄 输入文件: {input_file}")
    print(f"📊 文件大小: {file_size:.2f} MB")
    
    while True:
        show_menu()
        choice = get_user_choice()
        
        if choice == '0':
            print("👋 再见！")
            break
        elif choice == '1':
            process_method_1(input_file)
        elif choice == '2':
            process_method_2(input_file)
        elif choice == '3':
            process_method_3(input_file)
        elif choice == '4':
            process_method_4(input_file)
        elif choice == '5':
            process_batch(input_file)
        
        # 询问是否继续
        if choice != '5':  # 批量处理后不询问
            continue_choice = input("\n是否继续使用其他方法？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                print("👋 处理完成！")
                break

if __name__ == "__main__":
    main()
