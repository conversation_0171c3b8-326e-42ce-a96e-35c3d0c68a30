#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果汇总脚本 - 分析所有生成的PDF文件
"""

import os
import glob
from pathlib import Path

def analyze_results():
    """分析处理结果"""
    
    # 原文件
    original_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    
    # 查找所有生成的PDF文件
    generated_files = [
        "08-19 C语言实践项目（正文）-改版_裁剪法.pdf",
        "08-19 C语言实践项目（正文）-改版_文本删除法.pdf", 
        "08-19 C语言实践项目（正文）-改版_固定遮盖法.pdf",
        "08-19 C语言实践项目（正文）-改版_智能遮盖法.pdf"
    ]
    
    print("=" * 80)
    print("PDF页眉页脚删除结果汇总")
    print("=" * 80)
    
    # 原文件信息
    if os.path.exists(original_file):
        original_size = os.path.getsize(original_file) / (1024 * 1024)
        print(f"📄 原文件: {original_file}")
        print(f"📊 原文件大小: {original_size:.2f} MB")
        print()
    else:
        print("❌ 找不到原文件")
        return
    
    # 分析每个生成的文件
    results = []
    
    for i, file_path in enumerate(generated_files, 1):
        method_names = [
            "页面裁剪法",
            "文本检测删除法", 
            "固定区域遮盖法",
            "智能区域遮盖法"
        ]
        
        method_name = method_names[i-1]
        
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / (1024 * 1024)
            size_change = ((file_size - original_size) / original_size) * 100
            
            results.append({
                "method": method_name,
                "file": file_path,
                "size": file_size,
                "change": size_change,
                "status": "✅ 成功"
            })
            
            print(f"{i}. {method_name}")
            print(f"   📁 文件: {file_path}")
            print(f"   📊 大小: {file_size:.2f} MB ({size_change:+.1f}%)")
            print(f"   ✅ 状态: 处理成功")
            print()
        else:
            results.append({
                "method": method_name,
                "file": file_path,
                "size": 0,
                "change": 0,
                "status": "❌ 文件不存在"
            })
            
            print(f"{i}. {method_name}")
            print(f"   📁 文件: {file_path}")
            print(f"   ❌ 状态: 文件不存在")
            print()
    
    # 汇总表格
    print("=" * 80)
    print("处理结果对比表")
    print("=" * 80)
    print(f"{'方法':<15} {'文件大小(MB)':<12} {'大小变化':<10} {'状态':<10}")
    print("-" * 80)
    print(f"{'原文件':<15} {original_size:<12.2f} {'基准':<10} {'✅':<10}")
    
    for result in results:
        print(f"{result['method']:<15} {result['size']:<12.2f} {result['change']:+.1f}%{'':<6} {result['status']:<10}")
    
    print("=" * 80)
    
    # 推荐建议
    print("\n📋 方法对比分析:")
    print("-" * 50)
    
    print("1. 页面裁剪法:")
    print("   ✅ 优点: 文件大小减小，处理速度快")
    print("   ⚠️  注意: 可能裁剪掉部分正文内容")
    print()
    
    print("2. 文本检测删除法:")
    print("   ✅ 优点: 精确删除页眉页脚文本，不影响正文")
    print("   ⚠️  注意: 文件大小显著增加（可能是重新渲染导致）")
    print()
    
    print("3. 固定区域遮盖法:")
    print("   ✅ 优点: 可处理文本和图像，保持页面尺寸")
    print("   ⚠️  注意: 文件大小增加，留下空白区域")
    print()
    
    print("4. 智能区域遮盖法:")
    print("   ✅ 优点: 精确检测后遮盖，适应性强")
    print("   ⚠️  注意: 文件大小增加最多")
    print()
    
    print("🎯 推荐使用:")
    print("   • 如果追求文件大小: 选择页面裁剪法")
    print("   • 如果追求精确性: 选择文本检测删除法")
    print("   • 如果需要处理图像页眉页脚: 选择智能区域遮盖法")

if __name__ == "__main__":
    analyze_results()
