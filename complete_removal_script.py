#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整页眉页脚删除脚本 - 基于全面检测结果
"""

import fitz
import os

def complete_removal(input_pdf, output_pdf):
    """完整删除页眉页脚"""
    
    print(f"🎯 开始完整删除: {input_pdf}")
    
    # 确认的页眉文本
    confirmed_headers = {
        '任务知识',
        '动力电池基本知识',
        '新能源汽车动力电池构造与检修',
        '项目三',
    }
    
    # 项目标题模式
    project_patterns = ['项目一', '项目二', '项目三', '项目四', '项目五', '项目六', '项目七']
    
    try:
        doc = fitz.open(input_pdf)
        total_pages = len(doc)
        removed_count = 0
        
        for page_num in range(total_pages):
            if (page_num + 1) % 30 == 0 or page_num == 0:
                print(f"📈 处理进度: {page_num + 1}/{total_pages}")
            
            page = doc[page_num]
            text_dict = page.get_text("dict")
            areas_to_remove = []
            
            for block in text_dict["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"].strip()
                            if text:
                                bbox = span["bbox"]
                                y_pos = bbox[1]
                                
                                should_remove = False
                                
                                # 页眉检测（顶部150pt内）
                                if y_pos < 150:
                                    if (text in confirmed_headers or 
                                        text in project_patterns or
                                        text.startswith('项目')):
                                        should_remove = True
                                
                                # 页脚检测（底部80pt内）
                                elif y_pos > 674:
                                    if (text.isdigit() and 1 <= len(text) <= 3 and
                                        1 <= int(text) <= 200):
                                        should_remove = True
                                
                                if should_remove:
                                    areas_to_remove.append(bbox)
                                    removed_count += 1
            
            # 删除
            for bbox in areas_to_remove:
                rect_to_remove = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
                page.add_redact_annot(rect_to_remove, fill=(1, 1, 1))
            
            if areas_to_remove:
                page.apply_redactions()
        
        # 保存
        doc.save(output_pdf, garbage=4, deflate=True, clean=True)
        doc.close()
        
        print(f"✅ 完整删除完成！")
        print(f"📊 删除统计: {removed_count} 个元素")
        
        # 文件大小
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        size_change = ((new_size - original_size) / original_size) * 100
        
        print(f"📈 文件大小: {original_size:.2f} MB → {new_size:.2f} MB ({size_change:+.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 删除失败: {e}")
        return False

def main():
    input_file = "新能源汽车动力电池构造与检修（第二次正文）_未命名.pdf"
    output_file = "新能源汽车动力电池构造与检修（第二次正文）_完整删除.pdf"
    
    if not os.path.exists(input_file):
        print(f"❌ 找不到文件: {input_file}")
        return
    
    success = complete_removal(input_file, output_file)
    
    if success:
        print(f"🎉 处理完成！")
        print(f"📁 输出文件: {output_file}")

if __name__ == "__main__":
    main()
