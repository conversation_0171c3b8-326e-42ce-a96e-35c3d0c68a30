#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终结果汇总脚本 - 分析所有8种方法的处理结果
"""

import os
import glob
from pathlib import Path

def analyze_all_results():
    """分析所有处理结果"""
    
    # 原文件
    original_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    
    # 所有生成的PDF文件模式
    file_patterns = [
        # 原有3种方法
        "*裁剪法.pdf",
        "*文本删除法.pdf", 
        "*遮盖法.pdf",
        "*固定遮盖法.pdf",
        "*智能遮盖法.pdf",
        
        # 新增方法
        "*pdfplumber*.pdf",
        "*pypdf*.pdf",
        "*智能组合法.pdf",
        "*简化ai.pdf",
        "*简化AI法.pdf"
    ]
    
    print("=" * 100)
    print("🎯 PDF页眉页脚删除工具 - 8种方法终极对比")
    print("=" * 100)
    
    # 原文件信息
    if os.path.exists(original_file):
        original_size = os.path.getsize(original_file) / (1024 * 1024)
        print(f"📄 原文件: {original_file}")
        print(f"📊 原文件大小: {original_size:.2f} MB")
        print()
    else:
        print("❌ 找不到原文件")
        return
    
    # 查找所有生成的文件
    all_files = []
    for pattern in file_patterns:
        files = glob.glob(pattern)
        all_files.extend(files)
    
    # 去重并排序
    all_files = list(set(all_files))
    all_files.sort()
    
    # 分类文件
    method_categories = {
        "原有方法": {
            "PyMuPDF页面裁剪法": [],
            "PyMuPDF文本检测删除法": [],
            "PyMuPDF区域遮盖法": []
        },
        "新增方法": {
            "pdfplumber精确提取法": [],
            "pypdf现代处理法": [],
            "组合智能法": [],
            "简化AI重构法": []
        }
    }
    
    # 文件分类
    for file_path in all_files:
        file_name = file_path.lower()
        
        if "裁剪法" in file_name and "pypdf" not in file_name:
            method_categories["原有方法"]["PyMuPDF页面裁剪法"].append(file_path)
        elif "文本删除法" in file_name or "文本检测" in file_name:
            method_categories["原有方法"]["PyMuPDF文本检测删除法"].append(file_path)
        elif "遮盖法" in file_name and "智能组合" not in file_name:
            method_categories["原有方法"]["PyMuPDF区域遮盖法"].append(file_path)
        elif "pdfplumber" in file_name:
            method_categories["新增方法"]["pdfplumber精确提取法"].append(file_path)
        elif "pypdf" in file_name:
            method_categories["新增方法"]["pypdf现代处理法"].append(file_path)
        elif "智能组合" in file_name or "智能" in file_name:
            method_categories["新增方法"]["组合智能法"].append(file_path)
        elif "简化ai" in file_name or "ai" in file_name:
            method_categories["新增方法"]["简化AI重构法"].append(file_path)
    
    # 显示分类结果
    all_results = []
    
    for category, methods in method_categories.items():
        print(f"\n📋 {category}:")
        print("-" * 80)
        
        for method_name, files in methods.items():
            if files:
                # 选择最新的文件（如果有多个）
                latest_file = max(files, key=os.path.getmtime)
                
                if os.path.exists(latest_file):
                    file_size = os.path.getsize(latest_file) / (1024 * 1024)
                    size_change = ((file_size - original_size) / original_size) * 100
                    
                    result = {
                        "category": category,
                        "method": method_name,
                        "file": latest_file,
                        "size": file_size,
                        "change": size_change,
                        "status": "✅ 成功"
                    }
                    
                    all_results.append(result)
                    
                    print(f"  {method_name:<25} {latest_file:<40} {file_size:>8.2f} MB ({size_change:+6.1f}%)")
                else:
                    print(f"  {method_name:<25} {'文件不存在':<40} {'N/A':>8} {'N/A':>10}")
            else:
                print(f"  {method_name:<25} {'未找到输出文件':<40} {'N/A':>8} {'N/A':>10}")
    
    # 综合对比表
    print(f"\n{'='*100}")
    print("📊 综合对比表")
    print(f"{'='*100}")
    print(f"{'方法':<25} {'类别':<10} {'文件大小(MB)':<12} {'大小变化':<12} {'推荐指数':<10}")
    print("-" * 100)
    
    # 按文件大小排序
    all_results.sort(key=lambda x: x['size'])
    
    for result in all_results:
        # 计算推荐指数
        recommendation = calculate_recommendation_score(result)
        
        print(f"{result['method']:<25} {result['category']:<10} {result['size']:<12.2f} "
              f"{result['change']:+.1f}%{'':<8} {recommendation:<10}")
    
    # 推荐建议
    print(f"\n{'='*100}")
    print("🎯 推荐建议")
    print(f"{'='*100}")
    
    # 找出最佳方法
    best_methods = get_best_methods(all_results)
    
    for scenario, method_info in best_methods.items():
        print(f"\n🔸 {scenario}:")
        print(f"   推荐方法: {method_info['method']}")
        print(f"   文件大小: {method_info['size']:.2f} MB ({method_info['change']:+.1f}%)")
        print(f"   推荐理由: {method_info['reason']}")

def calculate_recommendation_score(result):
    """计算推荐指数"""
    
    size_change = result['change']
    
    # 基于文件大小变化计算分数
    if -10 <= size_change <= 10:  # 大小变化在±10%以内
        score = "⭐⭐⭐⭐⭐"
    elif -30 <= size_change <= 30:  # 大小变化在±30%以内
        score = "⭐⭐⭐⭐"
    elif -50 <= size_change <= 50:  # 大小变化在±50%以内
        score = "⭐⭐⭐"
    elif size_change < -50:  # 大幅减小
        score = "⭐⭐⭐⭐"  # 文件减小通常是好事
    else:  # 大幅增大
        score = "⭐⭐"
    
    return score

def get_best_methods(results):
    """获取不同场景下的最佳方法"""
    
    best_methods = {}
    
    # 最小文件大小
    smallest = min(results, key=lambda x: x['size'])
    best_methods["追求最小文件大小"] = {
        "method": smallest['method'],
        "size": smallest['size'],
        "change": smallest['change'],
        "reason": "文件大小最小，节省存储空间"
    }
    
    # 最接近原始大小
    closest_to_original = min(results, key=lambda x: abs(x['change']))
    best_methods["保持原始文件大小"] = {
        "method": closest_to_original['method'],
        "size": closest_to_original['size'],
        "change": closest_to_original['change'],
        "reason": "文件大小变化最小，保持原始质量"
    }
    
    # 新方法中的最佳
    new_methods = [r for r in results if r['category'] == "新增方法"]
    if new_methods:
        best_new = min(new_methods, key=lambda x: abs(x['change']))
        best_methods["新技术最佳选择"] = {
            "method": best_new['method'],
            "size": best_new['size'],
            "change": best_new['change'],
            "reason": "新技术中表现最佳的方法"
        }
    
    # 原有方法中的最佳
    original_methods = [r for r in results if r['category'] == "原有方法"]
    if original_methods:
        best_original = min(original_methods, key=lambda x: abs(x['change']))
        best_methods["传统方法最佳选择"] = {
            "method": best_original['method'],
            "size": best_original['size'],
            "change": best_original['change'],
            "reason": "传统方法中表现最佳的方法"
        }
    
    return best_methods

def main():
    """主函数"""
    analyze_all_results()

if __name__ == "__main__":
    main()
