#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方案1：页面裁剪法删除PDF页眉页脚
通过裁剪页面边距来去除页眉页脚区域
"""

import fitz  # PyMuPDF
import os
from pathlib import Path

def remove_header_footer_by_crop(input_pdf, output_pdf, 
                                top_margin=50, bottom_margin=50, 
                                left_margin=0, right_margin=0):
    """
    通过裁剪页面边距删除页眉页脚
    
    Args:
        input_pdf (str): 输入PDF文件路径
        output_pdf (str): 输出PDF文件路径
        top_margin (float): 顶部裁剪边距（像素）
        bottom_margin (float): 底部裁剪边距（像素）
        left_margin (float): 左侧裁剪边距（像素）
        right_margin (float): 右侧裁剪边距（像素）
    """
    
    print(f"开始处理PDF文件: {input_pdf}")
    print(f"裁剪参数 - 顶部: {top_margin}px, 底部: {bottom_margin}px, 左侧: {left_margin}px, 右侧: {right_margin}px")
    
    # 打开原PDF文件
    doc = fitz.open(input_pdf)
    total_pages = len(doc)
    print(f"总页数: {total_pages}")
    
    # 创建新的PDF文档
    new_doc = fitz.open()
    
    try:
        for page_num in range(total_pages):
            # 显示进度
            if (page_num + 1) % 50 == 0 or page_num == 0:
                print(f"处理进度: {page_num + 1}/{total_pages}")
            
            # 获取原页面
            page = doc[page_num]
            original_rect = page.rect
            
            # 计算新的页面矩形（裁剪后）
            new_rect = fitz.Rect(
                original_rect.x0 + left_margin,    # 左边界
                original_rect.y0 + top_margin,     # 上边界  
                original_rect.x1 - right_margin,   # 右边界
                original_rect.y1 - bottom_margin   # 下边界
            )
            
            # 创建新页面
            new_page = new_doc.new_page(width=new_rect.width, height=new_rect.height)
            
            # 将原页面内容复制到新页面（带偏移）
            new_page.show_pdf_page(
                new_page.rect,  # 目标矩形
                doc,            # 源文档
                page_num,       # 源页面号
                clip=new_rect   # 裁剪区域
            )
        
        # 保存新PDF
        new_doc.save(output_pdf)
        print(f"✅ 裁剪完成！输出文件: {output_pdf}")
        
        # 显示文件大小对比
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        print(f"文件大小对比:")
        print(f"  原文件: {original_size:.2f} MB")
        print(f"  新文件: {new_size:.2f} MB")
        print(f"  压缩率: {((original_size - new_size) / original_size * 100):.1f}%")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
    finally:
        doc.close()
        new_doc.close()

def main():
    """主函数"""
    input_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    output_file = "08-19 C语言实践项目（正文）-改版_裁剪法.pdf"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误：找不到输入文件 {input_file}")
        return
    
    print("=" * 60)
    print("PDF页眉页脚删除工具 - 页面裁剪法")
    print("=" * 60)
    
    # 执行裁剪（根据之前分析，页脚在底部，所以主要裁剪底部）
    remove_header_footer_by_crop(
        input_pdf=input_file,
        output_pdf=output_file,
        top_margin=30,      # 顶部裁剪30像素（预防页眉）
        bottom_margin=40,   # 底部裁剪40像素（删除页脚页码）
        left_margin=0,      # 左侧不裁剪
        right_margin=0      # 右侧不裁剪
    )

if __name__ == "__main__":
    main()
