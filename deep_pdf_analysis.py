#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度PDF分析工具 - 精确定位页眉页脚问题
"""

import fitz  # PyMuPDF
import pdfplumber
import os

def deep_analyze_pdf(pdf_path):
    """深度分析PDF文件"""
    
    print(f"🔍 深度分析PDF文件: {pdf_path}")
    print("="*80)
    
    # 使用PyMuPDF分析
    doc = fitz.open(pdf_path)
    
    # 分析多个页面
    sample_pages = [0, 1, 2, 10, 50, 100, 200, 299]  # 分析不同位置的页面
    sample_pages = [p for p in sample_pages if p < len(doc)]
    
    print(f"📊 总页数: {len(doc)}")
    print(f"🔍 分析页面: {sample_pages}")
    print()
    
    for page_num in sample_pages:
        page = doc[page_num]
        rect = page.rect
        
        print(f"📄 第 {page_num + 1} 页分析:")
        print(f"   页面尺寸: {rect.width:.1f} x {rect.height:.1f}")
        
        # 获取所有文本块的详细信息
        text_dict = page.get_text("dict")
        
        # 分析页面顶部和底部的内容
        top_content = []
        bottom_content = []
        middle_content = []
        
        for block in text_dict["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]
                            y_pos = bbox[1]  # 顶部y坐标
                            
                            # 分类内容
                            if y_pos < rect.height * 0.1:  # 顶部10%
                                top_content.append({
                                    "text": text,
                                    "y": y_pos,
                                    "bbox": bbox,
                                    "font": span.get("font", ""),
                                    "size": span.get("size", 0)
                                })
                            elif y_pos > rect.height * 0.9:  # 底部10%
                                bottom_content.append({
                                    "text": text,
                                    "y": y_pos,
                                    "bbox": bbox,
                                    "font": span.get("font", ""),
                                    "size": span.get("size", 0)
                                })
                            else:
                                middle_content.append({
                                    "text": text[:50] + "..." if len(text) > 50 else text,
                                    "y": y_pos
                                })
        
        # 显示顶部内容
        if top_content:
            print(f"   🔝 页面顶部内容:")
            for item in sorted(top_content, key=lambda x: x['y']):
                print(f"      '{item['text']}' - Y位置: {item['y']:.1f}, 字体: {item['font']}, 大小: {item['size']:.1f}")
        else:
            print(f"   🔝 页面顶部: 无内容")
        
        # 显示底部内容
        if bottom_content:
            print(f"   🔽 页面底部内容:")
            for item in sorted(bottom_content, key=lambda x: x['y'], reverse=True):
                print(f"      '{item['text']}' - Y位置: {item['y']:.1f}, 字体: {item['font']}, 大小: {item['size']:.1f}")
        else:
            print(f"   🔽 页面底部: 无内容")
        
        # 显示中间内容示例
        if middle_content:
            print(f"   📄 正文内容示例:")
            for i, item in enumerate(sorted(middle_content, key=lambda x: x['y'], reverse=True)[:3]):
                print(f"      '{item['text']}' - Y位置: {item['y']:.1f}")
        
        print()
    
    doc.close()
    
    # 使用pdfplumber进行补充分析
    print("="*80)
    print("🔍 pdfplumber补充分析:")
    
    with pdfplumber.open(pdf_path) as pdf:
        # 分析第一页和最后一页
        for page_num in [0, len(pdf.pages)-1]:
            if page_num < len(pdf.pages):
                page = pdf.pages[page_num]
                
                print(f"\n📄 第 {page_num + 1} 页 (pdfplumber):")
                
                # 获取页面文本
                text = page.extract_text()
                if text:
                    lines = text.split('\n')
                    print(f"   总行数: {len(lines)}")
                    
                    # 显示前3行和后3行
                    print(f"   前3行:")
                    for i, line in enumerate(lines[:3]):
                        if line.strip():
                            print(f"      {i+1}: '{line.strip()}'")
                    
                    print(f"   后3行:")
                    for i, line in enumerate(lines[-3:]):
                        if line.strip():
                            print(f"      {len(lines)-3+i+1}: '{line.strip()}'")
                
                # 分析表格
                tables = page.extract_tables()
                if tables:
                    print(f"   检测到 {len(tables)} 个表格")
                
                # 分析字符信息
                chars = page.chars
                if chars:
                    y_positions = [char['y0'] for char in chars]
                    print(f"   Y坐标范围: {min(y_positions):.1f} - {max(y_positions):.1f}")

def find_precise_header_footer_bounds(pdf_path):
    """精确找到页眉页脚边界"""
    
    print("\n" + "="*80)
    print("🎯 精确定位页眉页脚边界")
    print("="*80)
    
    doc = fitz.open(pdf_path)
    
    # 分析多页来确定一致的页眉页脚位置
    header_bounds = []
    footer_bounds = []
    
    sample_pages = min(10, len(doc))
    
    for page_num in range(sample_pages):
        page = doc[page_num]
        rect = page.rect
        
        text_dict = page.get_text("dict")
        
        page_texts = []
        for block in text_dict["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]
                            page_texts.append({
                                "text": text,
                                "y_top": bbox[1],
                                "y_bottom": bbox[3],
                                "x_left": bbox[0],
                                "x_right": bbox[2]
                            })
        
        if page_texts:
            # 按Y坐标排序
            page_texts.sort(key=lambda x: x['y_top'], reverse=True)
            
            # 找到最顶部和最底部的文本
            top_text = page_texts[0]
            bottom_text = page_texts[-1]
            
            # 检查是否可能是页眉页脚
            if is_likely_header_footer_text(top_text['text']):
                header_bounds.append(rect.height - top_text['y_bottom'])
            
            if is_likely_header_footer_text(bottom_text['text']):
                footer_bounds.append(bottom_text['y_top'])
    
    doc.close()
    
    # 计算平均边界
    if header_bounds:
        avg_header = sum(header_bounds) / len(header_bounds)
        print(f"🔝 检测到页眉，平均高度: {avg_header:.1f}pt")
        print(f"   各页页眉高度: {[f'{h:.1f}' for h in header_bounds]}")
    else:
        avg_header = 0
        print(f"🔝 未检测到明显页眉")
    
    if footer_bounds:
        avg_footer = sum(footer_bounds) / len(footer_bounds)
        print(f"🔽 检测到页脚，平均位置: {avg_footer:.1f}pt")
        print(f"   各页页脚位置: {[f'{f:.1f}' for f in footer_bounds]}")
    else:
        avg_footer = 0
        print(f"🔽 未检测到明显页脚")
    
    return avg_header, avg_footer

def is_likely_header_footer_text(text):
    """判断文本是否可能是页眉页脚"""
    
    text = text.strip().lower()
    
    # 页码模式
    if text.isdigit() and len(text) <= 4:
        return True
    
    # 常见页眉页脚关键词
    keywords = ['第', '页', 'page', '章', 'chapter', '目录', '版权', '保密']
    for keyword in keywords:
        if keyword in text:
            return True
    
    # 短文本（通常页眉页脚比较简短）
    if len(text) < 20:
        return True
    
    return False

def main():
    """主函数"""
    input_file = "新能源汽车动力电池构造与检修（第二次正文）_未命名.pdf"
    
    if not os.path.exists(input_file):
        print(f"❌ 找不到文件: {input_file}")
        return
    
    # 深度分析
    deep_analyze_pdf(input_file)
    
    # 精确定位边界
    header_height, footer_pos = find_precise_header_footer_bounds(input_file)
    
    print(f"\n🎯 建议的处理参数:")
    print(f"   页眉裁剪高度: {header_height:.1f}pt")
    print(f"   页脚裁剪高度: {754 - footer_pos:.1f}pt (从底部)")

if __name__ == "__main__":
    main()
