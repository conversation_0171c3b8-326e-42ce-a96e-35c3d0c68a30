#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方案2：文本检测删除法删除PDF页眉页脚
检测页眉页脚区域的文本并精确删除
"""

import fitz  # PyMuPDF
import os
import re

def is_likely_header_footer_text(text, bbox, page_rect, page_num):
    """
    判断文本是否可能是页眉页脚
    
    Args:
        text (str): 文本内容
        bbox (tuple): 文本边界框 (x0, y0, x1, y1)
        page_rect (fitz.Rect): 页面矩形
        page_num (int): 页码（从0开始）
    
    Returns:
        bool: True表示可能是页眉页脚
    """
    
    # 计算文本位置
    text_top = bbox[1]
    text_bottom = bbox[3]
    page_height = page_rect.height
    
    # 页眉区域：页面顶部15%
    header_threshold = page_height * 0.15
    # 页脚区域：页面底部15%  
    footer_threshold = page_height * 0.85
    
    # 位置判断
    in_header_area = text_top < header_threshold
    in_footer_area = text_bottom > footer_threshold
    
    if not (in_header_area or in_footer_area):
        return False
    
    # 文本内容判断
    text_clean = text.strip()
    
    # 常见页眉页脚模式
    patterns = [
        r'^\d+$',                           # 纯数字（页码）
        r'^第\s*\d+\s*页$',                 # "第X页"
        r'^页\s*\d+$',                      # "页X"
        r'^\d+\s*/\s*\d+$',                 # "X/Y"格式页码
        r'^-\s*\d+\s*-$',                   # "-X-"格式页码
        r'^\(\s*\d+\s*\)$',                 # "(X)"格式页码
        r'^第.*章$',                        # 章节标题
        r'^.*目录.*$',                      # 目录相关
        r'^.*版权.*$',                      # 版权信息
        r'^.*保密.*$',                      # 保密信息
        r'^\d{4}[-/]\d{1,2}[-/]\d{1,2}$',   # 日期格式
    ]
    
    # 检查是否匹配页眉页脚模式
    for pattern in patterns:
        if re.match(pattern, text_clean, re.IGNORECASE):
            return True
    
    # 短文本且在页眉页脚区域
    if len(text_clean) <= 20 and (in_header_area or in_footer_area):
        return True
    
    return False

def remove_header_footer_by_text_detection(input_pdf, output_pdf):
    """
    通过文本检测删除页眉页脚
    
    Args:
        input_pdf (str): 输入PDF文件路径
        output_pdf (str): 输出PDF文件路径
    """
    
    print(f"开始处理PDF文件: {input_pdf}")
    
    # 打开PDF文件
    doc = fitz.open(input_pdf)
    total_pages = len(doc)
    print(f"总页数: {total_pages}")
    
    removed_count = 0
    
    try:
        for page_num in range(total_pages):
            # 显示进度
            if (page_num + 1) % 50 == 0 or page_num == 0:
                print(f"处理进度: {page_num + 1}/{total_pages}")
            
            page = doc[page_num]
            page_rect = page.rect
            
            # 获取页面上的所有文本块
            text_dict = page.get_text("dict")
            
            # 收集需要删除的文本区域
            areas_to_remove = []
            
            for block in text_dict["blocks"]:
                if "lines" in block:  # 文本块
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"]
                            bbox = span["bbox"]
                            
                            # 判断是否为页眉页脚文本
                            if is_likely_header_footer_text(text, bbox, page_rect, page_num):
                                areas_to_remove.append({
                                    "text": text.strip(),
                                    "bbox": bbox
                                })
                                removed_count += 1
            
            # 删除识别出的页眉页脚文本
            for area in areas_to_remove:
                bbox = area["bbox"]
                # 创建稍大一点的矩形确保完全覆盖
                rect = fitz.Rect(bbox[0] - 2, bbox[1] - 2, bbox[2] + 2, bbox[3] + 2)
                
                # 使用redact功能删除文本
                page.add_redact_annot(rect)
            
            # 应用删除操作
            if areas_to_remove:
                page.apply_redactions()
        
        # 保存修改后的PDF
        doc.save(output_pdf)
        print(f"✅ 文本删除完成！输出文件: {output_pdf}")
        print(f"总共删除了 {removed_count} 个页眉页脚文本元素")
        
        # 显示文件大小对比
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        print(f"文件大小对比:")
        print(f"  原文件: {original_size:.2f} MB")
        print(f"  新文件: {new_size:.2f} MB")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
    finally:
        doc.close()

def main():
    """主函数"""
    input_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    output_file = "08-19 C语言实践项目（正文）-改版_文本删除法.pdf"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误：找不到输入文件 {input_file}")
        return
    
    print("=" * 60)
    print("PDF页眉页脚删除工具 - 文本检测删除法")
    print("=" * 60)
    
    # 执行文本删除
    remove_header_footer_by_text_detection(input_file, output_file)

if __name__ == "__main__":
    main()
