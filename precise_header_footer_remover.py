#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确页眉页脚删除工具 - 基于深度分析的精确删除
根据实际分析结果，精确删除页眉页脚，保持原始格式和合理文件大小
"""

import fitz  # PyMuPDF
import os
import time

def precise_remove_header_footer(input_pdf, output_pdf):
    """
    精确删除页眉页脚
    
    基于分析结果：
    - 页眉：Y位置约42.2pt，内容为项目标题
    - 页脚：Y位置约700.1pt，内容为页码
    - 页面高度：754.0pt
    """
    
    print(f"🎯 开始精确删除页眉页脚: {input_pdf}")
    print("基于深度分析的精确参数:")
    print("  - 页眉区域: Y < 60pt (项目标题)")
    print("  - 页脚区域: Y > 690pt (页码)")
    print("  - 保持原始格式和质量")
    
    try:
        doc = fitz.open(input_pdf)
        total_pages = len(doc)
        print(f"总页数: {total_pages}")
        
        removed_elements = 0
        
        for page_num in range(total_pages):
            if (page_num + 1) % 50 == 0 or page_num == 0:
                print(f"处理进度: {page_num + 1}/{total_pages}")
            
            page = doc[page_num]
            rect = page.rect
            
            # 获取页面上的所有文本块
            text_dict = page.get_text("dict")
            
            # 收集需要删除的区域
            areas_to_remove = []
            
            for block in text_dict["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"].strip()
                            bbox = span["bbox"]
                            y_pos = bbox[1]  # 顶部Y坐标
                            
                            # 精确判断页眉页脚
                            is_header = False
                            is_footer = False
                            
                            # 页眉判断：Y < 60pt 且包含项目相关内容
                            if y_pos < 60:
                                if any(keyword in text for keyword in ['项目', 'C语言', '程序设计', '指针', '数组', '函数']):
                                    is_header = True
                                elif text.isdigit() and len(text) <= 2:  # 项目编号
                                    is_header = True
                            
                            # 页脚判断：Y > 690pt 且为页码
                            elif y_pos > 690:
                                if text.isdigit() and len(text) <= 4:  # 页码
                                    is_footer = True
                            
                            if is_header or is_footer:
                                areas_to_remove.append({
                                    "text": text,
                                    "bbox": bbox,
                                    "type": "页眉" if is_header else "页脚"
                                })
                                removed_elements += 1
            
            # 删除识别出的页眉页脚
            for area in areas_to_remove:
                bbox = area["bbox"]
                # 创建稍大一点的矩形确保完全覆盖
                rect_to_remove = fitz.Rect(
                    bbox[0] - 2, 
                    bbox[1] - 2, 
                    bbox[2] + 2, 
                    bbox[3] + 2
                )
                
                # 使用redact功能删除
                page.add_redact_annot(rect_to_remove)
            
            # 应用删除操作
            if areas_to_remove:
                page.apply_redactions()
        
        # 保存修改后的PDF
        doc.save(output_pdf)
        doc.close()
        
        print(f"✅ 精确删除完成！")
        print(f"📊 删除统计:")
        print(f"   删除元素数量: {removed_elements}")
        print(f"   输出文件: {output_pdf}")
        
        # 显示文件大小对比
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        size_change = ((new_size - original_size) / original_size) * 100
        
        print(f"📈 文件大小对比:")
        print(f"   原文件: {original_size:.2f} MB")
        print(f"   新文件: {new_size:.2f} MB")
        print(f"   大小变化: {size_change:+.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 精确删除失败: {e}")
        return False

def minimal_crop_method(input_pdf, output_pdf):
    """
    最小裁剪法 - 只裁剪必要的页眉页脚区域
    """
    
    print(f"✂️ 开始最小裁剪处理: {input_pdf}")
    
    try:
        doc = fitz.open(input_pdf)
        new_doc = fitz.open()
        
        total_pages = len(doc)
        print(f"总页数: {total_pages}")
        
        for page_num in range(total_pages):
            if (page_num + 1) % 50 == 0 or page_num == 0:
                print(f"处理进度: {page_num + 1}/{total_pages}")
            
            page = doc[page_num]
            original_rect = page.rect
            
            # 最小裁剪：只裁剪页眉页脚区域
            # 页眉：裁剪顶部55pt（保留5pt缓冲）
            # 页脚：裁剪底部70pt（保留16pt缓冲）
            new_rect = fitz.Rect(
                original_rect.x0,           # 左边界不变
                original_rect.y0 + 55,      # 顶部裁剪55pt
                original_rect.x1,           # 右边界不变
                original_rect.y1 - 70       # 底部裁剪70pt
            )
            
            # 创建新页面
            new_page = new_doc.new_page(width=new_rect.width, height=new_rect.height)
            
            # 复制内容到新页面
            new_page.show_pdf_page(
                new_page.rect,
                doc,
                page_num,
                clip=new_rect
            )
        
        # 保存新PDF
        new_doc.save(output_pdf)
        new_doc.close()
        doc.close()
        
        print(f"✅ 最小裁剪完成！输出文件: {output_pdf}")
        
        # 显示文件大小对比
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        size_change = ((new_size - original_size) / original_size) * 100
        
        print(f"📈 文件大小对比:")
        print(f"   原文件: {original_size:.2f} MB")
        print(f"   新文件: {new_size:.2f} MB")
        print(f"   大小变化: {size_change:+.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 最小裁剪失败: {e}")
        return False

def conservative_text_removal(input_pdf, output_pdf):
    """
    保守文本删除法 - 只删除明确的页眉页脚文本
    """
    
    print(f"🔍 开始保守文本删除: {input_pdf}")
    
    try:
        doc = fitz.open(input_pdf)
        total_pages = len(doc)
        print(f"总页数: {total_pages}")
        
        removed_count = 0
        
        for page_num in range(total_pages):
            if (page_num + 1) % 50 == 0 or page_num == 0:
                print(f"处理进度: {page_num + 1}/{total_pages}")
            
            page = doc[page_num]
            rect = page.rect
            
            text_dict = page.get_text("dict")
            areas_to_remove = []
            
            for block in text_dict["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"].strip()
                            bbox = span["bbox"]
                            y_pos = bbox[1]
                            
                            # 非常保守的判断
                            should_remove = False
                            
                            # 只删除明确的页码（底部的纯数字）
                            if y_pos > 690 and text.isdigit() and 1 <= len(text) <= 4:
                                try:
                                    page_number = int(text)
                                    if 1 <= page_number <= 500:  # 合理的页码范围
                                        should_remove = True
                                except:
                                    pass
                            
                            # 只删除明确的项目标题（顶部的特定格式）
                            elif y_pos < 60:
                                if text in ['项目', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']:
                                    should_remove = True
                                elif text in ['C语言概述', '顺序结构程序设计', '指针及应用']:
                                    should_remove = True
                            
                            if should_remove:
                                areas_to_remove.append({
                                    "text": text,
                                    "bbox": bbox
                                })
                                removed_count += 1
            
            # 删除识别出的内容
            for area in areas_to_remove:
                bbox = area["bbox"]
                rect_to_remove = fitz.Rect(bbox[0] - 1, bbox[1] - 1, bbox[2] + 1, bbox[3] + 1)
                page.add_redact_annot(rect_to_remove)
            
            if areas_to_remove:
                page.apply_redactions()
        
        doc.save(output_pdf)
        doc.close()
        
        print(f"✅ 保守删除完成！")
        print(f"删除了 {removed_count} 个页眉页脚元素")
        print(f"输出文件: {output_pdf}")
        
        # 显示文件大小对比
        original_size = os.path.getsize(input_pdf) / (1024 * 1024)
        new_size = os.path.getsize(output_pdf) / (1024 * 1024)
        size_change = ((new_size - original_size) / original_size) * 100
        
        print(f"📈 文件大小对比:")
        print(f"   原文件: {original_size:.2f} MB")
        print(f"   新文件: {new_size:.2f} MB")
        print(f"   大小变化: {size_change:+.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 保守删除失败: {e}")
        return False

def main():
    """主函数"""
    input_file = "08-19 C语言实践项目（正文）-改版_未命名(4).pdf"
    
    if not os.path.exists(input_file):
        print(f"❌ 找不到文件: {input_file}")
        return
    
    print("🎯 精确页眉页脚删除工具")
    print("="*60)
    print("基于深度分析，提供3种精确删除方法：")
    print("1. 精确文本删除法 - 只删除页眉页脚文本")
    print("2. 最小裁剪法 - 最小化裁剪页眉页脚区域")
    print("3. 保守删除法 - 只删除明确的页眉页脚")
    print("="*60)
    
    # 方法1：精确文本删除
    print("\n🎯 方法1：精确文本删除法")
    output1 = "08-19 C语言实践项目（正文）-改版_精确删除.pdf"
    success1 = precise_remove_header_footer(input_file, output1)
    
    print("\n" + "="*60)
    
    # 方法2：最小裁剪
    print("\n✂️ 方法2：最小裁剪法")
    output2 = "08-19 C语言实践项目（正文）-改版_最小裁剪.pdf"
    success2 = minimal_crop_method(input_file, output2)
    
    print("\n" + "="*60)
    
    # 方法3：保守删除
    print("\n🔍 方法3：保守删除法")
    output3 = "08-19 C语言实践项目（正文）-改版_保守删除.pdf"
    success3 = conservative_text_removal(input_file, output3)
    
    # 总结
    print("\n" + "="*60)
    print("📊 处理结果总结:")
    methods = [
        ("精确文本删除法", output1, success1),
        ("最小裁剪法", output2, success2),
        ("保守删除法", output3, success3)
    ]
    
    for method_name, output_file, success in methods:
        if success and os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)
            print(f"✅ {method_name}: {output_file} ({file_size:.2f} MB)")
        else:
            print(f"❌ {method_name}: 处理失败")

if __name__ == "__main__":
    main()
